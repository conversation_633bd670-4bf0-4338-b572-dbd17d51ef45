#!/usr/bin/env python3
# -*- coding: utf-8 -*-

import sys
from PyQt5.QtWidgets import QApplication, QMainWindow, QWidget, QLabel, QPushButton
from PyQt5.QtCore import Qt
from PyQt5.QtGui import QFont
import datetime

class DuplicateChartsDebugger(QMainWindow):
    def __init__(self):
        super().__init__()
        self.setWindowTitle("重复图表调试器 - 检查底部残留")
        # 设置1800x1000界面尺寸
        self.setGeometry(50, 50, 1800, 1000)
        
        # 设置深色主题
        self.setStyleSheet("""
            QMainWindow {
                background-color: #1e1e1e;
                color: white;
            }
            QLabel {
                color: #00FFFF;
                font-size: 12px;
                padding: 5px;
            }
            QPushButton {
                background-color: #2d5a87;
                color: white;
                border: 2px solid #00FFFF;
                border-radius: 5px;
                padding: 8px 16px;
                font-size: 12px;
                font-weight: bold;
            }
        """)
        
        # 创建中央部件
        central_widget = QWidget()
        self.setCentralWidget(central_widget)
        
        self.setup_debug_interface()
        
    def setup_debug_interface(self):
        """设置调试界面"""
        parent = self.centralWidget()
        
        # 调试信息显示
        debug_info = QLabel(f"""重复图表调试报告:

问题描述: 界面底部出现注水/吹除平衡图表的残留部分

可能原因分析:
1. 图表创建时父窗口设置不正确
2. 图表尺寸限制导致显示异常
3. 存在多个图表实例
4. 图表布局管理器冲突

修复措施:
✅ 修改图表父窗口: 从 self.Setparameter 改为 self
✅ 添加旧图表清理: close() + deleteLater()
✅ 移除图表尺寸限制: 允许动态调整
✅ 增加界面宽度: 1600 → 1800 像素

当前图表位置:
• 注水/吹除平衡图表: (1200, 500, 550, 450)
• 图表右边界: 1200 + 550 = 1750 像素
• 图表下边界: 500 + 450 = 950 像素
• 界面边界: 1800 × 1000 像素

安全检查:
✅ 水平不超界: 1750 < 1800 (安全间距50px)
✅ 垂直不超界: 950 < 1000 (安全间距50px)

如果仍有底部残留，可能是:
1. 旧的图表实例没有完全清理
2. matplotlib画布缓存问题
3. PyQt5布局管理器问题

建议解决方案:
1. 重启程序清理所有实例
2. 检查是否有其他代码创建了图表
3. 确保图表正确设置父窗口关系""", parent)
        
        debug_info.setGeometry(50, 50, 700, 600)
        debug_info.setAlignment(Qt.AlignTop | Qt.AlignLeft)
        debug_info.setStyleSheet("""
            background-color: rgba(0, 0, 0, 0.8);
            color: white;
            font-size: 11px;
            border: 2px solid #00FFFF;
            border-radius: 5px;
            padding: 15px;
        """)
        debug_info.setWordWrap(True)
        
        # 模拟正确位置的图表区域
        correct_chart_area = QLabel("正确的注水图表位置\n(1200, 500, 550, 450)", parent)
        correct_chart_area.setGeometry(1200, 500, 550, 450)
        correct_chart_area.setAlignment(Qt.AlignCenter)
        correct_chart_area.setStyleSheet("""
            background-color: rgba(0, 255, 0, 0.3);
            border: 3px solid #00FF00;
            color: #00FF00;
            font-size: 16px;
            font-weight: bold;
        """)
        
        # 检查底部区域
        bottom_check_area = QLabel("检查区域：底部是否有残留图表", parent)
        bottom_check_area.setGeometry(50, 950, 1700, 40)
        bottom_check_area.setAlignment(Qt.AlignCenter)
        bottom_check_area.setStyleSheet("""
            background-color: rgba(255, 0, 0, 0.3);
            border: 2px dashed #FF0000;
            color: #FF0000;
            font-size: 14px;
            font-weight: bold;
        """)
        
        # 创建测试按钮
        test_btn = QPushButton("测试：创建单个图表", parent)
        test_btn.setGeometry(800, 100, 200, 50)
        test_btn.clicked.connect(self.test_single_chart)
        
        clear_btn = QPushButton("清理：删除所有图表", parent)
        clear_btn.setGeometry(800, 170, 200, 50)
        clear_btn.clicked.connect(self.clear_all_charts)
        
        # 状态显示
        self.status_label = QLabel("状态: 调试器已启动", parent)
        self.status_label.setGeometry(800, 250, 400, 30)
        self.status_label.setStyleSheet("color: #00FFFF; font-size: 14px; font-weight: bold;")
        
    def test_single_chart(self):
        """测试创建单个图表"""
        try:
            from water_balance_chart import WaterBalanceChart
            
            # 清理可能存在的旧图表
            if hasattr(self, 'test_chart'):
                self.test_chart.close()
                self.test_chart.deleteLater()
                delattr(self, 'test_chart')
            
            # 创建新图表
            self.test_chart = WaterBalanceChart(self)
            self.test_chart.setGeometry(1200, 500, 550, 450)
            self.test_chart.show()
            
            self.status_label.setText("状态: 单个测试图表已创建")
            self.status_label.setStyleSheet("color: #00FF00; font-size: 14px; font-weight: bold;")
            print("测试图表创建成功")
            
        except Exception as e:
            self.status_label.setText(f"状态: 创建失败 - {e}")
            self.status_label.setStyleSheet("color: #FF0000; font-size: 14px; font-weight: bold;")
            print(f"测试图表创建失败: {e}")
    
    def clear_all_charts(self):
        """清理所有图表"""
        try:
            if hasattr(self, 'test_chart'):
                self.test_chart.close()
                self.test_chart.deleteLater()
                delattr(self, 'test_chart')
            
            self.status_label.setText("状态: 所有图表已清理")
            self.status_label.setStyleSheet("color: #FFFF00; font-size: 14px; font-weight: bold;")
            print("所有图表已清理")
            
        except Exception as e:
            self.status_label.setText(f"状态: 清理失败 - {e}")
            self.status_label.setStyleSheet("color: #FF0000; font-size: 14px; font-weight: bold;")
            print(f"清理失败: {e}")

def main():
    app = QApplication(sys.argv)
    app.setStyle('Fusion')
    
    window = DuplicateChartsDebugger()
    window.show()
    
    print("重复图表调试器启动")
    print("请检查界面底部是否有残留的图表部分")
    
    sys.exit(app.exec_())

if __name__ == "__main__":
    main()
