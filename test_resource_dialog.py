#!/usr/bin/env python3
# -*- coding: utf-8 -*-

"""
测试资源对话框的修改
"""

import sys
import os

# 添加QTproject-master到路径
sys.path.append(os.path.join(os.path.dirname(__file__), 'QTproject-master'))

from PyQt5.QtWidgets import QApplication, QPushButton, QVBoxLayout, QWidget
from resource_dialog import ResourceDialog

class TestWindow(QWidget):
    def __init__(self):
        super().__init__()
        self.setWindowTitle('测试资源对话框')
        self.setGeometry(100, 100, 300, 200)
        
        layout = QVBoxLayout()
        
        # 测试按钮
        test_btn = QPushButton('打开资源设置对话框 (16发导弹)')
        test_btn.clicked.connect(self.open_resource_dialog)
        layout.addWidget(test_btn)
        
        self.setLayout(layout)
    
    def open_resource_dialog(self):
        # 测试16发导弹的资源设置
        dialog = ResourceDialog(self, 16)
        if dialog.exec_():
            resources = dialog.getResources()
            print("设置的资源状态:")
            for missile_id, resource_data in resources.items():
                print(f"DD{missile_id}: 气源={resource_data['gas']}, 水源={resource_data['water']}, 液压源={resource_data['hydraulic']}")

if __name__ == '__main__':
    app = QApplication(sys.argv)
    window = TestWindow()
    window.show()
    sys.exit(app.exec_())
