#!/usr/bin/env python3
# -*- coding: utf-8 -*-

"""
测试DQN算法对资源状态的处理
"""

import sys
import os
from missile_launch_rl import get_optimal_sequence

def test_dqn_with_resources():
    """测试DQN算法处理资源状态"""
    
    # 测试参数
    tdistance_path = "Tdistance.xlsx"  # 假设存在这个文件
    columns_count = 2  # 2列，共4发导弹
    first_missile_id = 1
    
    # 测试场景1：所有资源都OK
    print("=" * 60)
    print("测试场景1：所有资源都OK")
    resource_states_all_ok = {
        1: {'gas': 'OK', 'water': 'OK', 'hydraulic': 'OK'},
        2: {'gas': 'OK', 'water': 'OK', 'hydraulic': 'OK'},
        3: {'gas': 'OK', 'water': 'OK', 'hydraulic': 'OK'},
        4: {'gas': 'OK', 'water': 'OK', 'hydraulic': 'OK'}
    }
    
    try:
        sequence1, score1 = get_optimal_sequence(
            tdistance_path, 
            columns_count, 
            first_missile_id, 
            verbose=True, 
            excluded_missiles=None,
            resource_states=resource_states_all_ok
        )
        print(f"序列: {sequence1}")
        print(f"评分: {score1:.4f}")
    except Exception as e:
        print(f"错误: {e}")
    
    # 测试场景2：部分资源FAIL
    print("\n" + "=" * 60)
    print("测试场景2：导弹2和4的气源FAIL")
    resource_states_partial_fail = {
        1: {'gas': 'OK', 'water': 'OK', 'hydraulic': 'OK'},
        2: {'gas': 'FAIL', 'water': 'OK', 'hydraulic': 'OK'},  # 气源故障
        3: {'gas': 'OK', 'water': 'OK', 'hydraulic': 'OK'},
        4: {'gas': 'FAIL', 'water': 'OK', 'hydraulic': 'OK'}   # 气源故障
    }
    
    try:
        sequence2, score2 = get_optimal_sequence(
            tdistance_path, 
            columns_count, 
            first_missile_id, 
            verbose=True, 
            excluded_missiles=None,
            resource_states=resource_states_partial_fail
        )
        print(f"序列: {sequence2}")
        print(f"评分: {score2:.4f}")
        print("注意：导弹2和4应该被排除，因为气源故障")
    except Exception as e:
        print(f"错误: {e}")
    
    # 测试场景3：无资源状态信息（默认行为）
    print("\n" + "=" * 60)
    print("测试场景3：无资源状态信息（默认所有资源OK）")
    
    try:
        sequence3, score3 = get_optimal_sequence(
            tdistance_path, 
            columns_count, 
            first_missile_id, 
            verbose=True, 
            excluded_missiles=None,
            resource_states=None
        )
        print(f"序列: {sequence3}")
        print(f"评分: {score3:.4f}")
    except Exception as e:
        print(f"错误: {e}")

if __name__ == '__main__':
    test_dqn_with_resources()
