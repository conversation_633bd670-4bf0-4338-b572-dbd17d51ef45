#!/usr/bin/env python3
# -*- coding: utf-8 -*-

"""
测试双图表集成效果
验证邻桶效应图表和注水/吹除平衡图表的协同工作
"""

import sys
from PyQt5.QtWidgets import QApplication, QMainWindow, QWidget, QLabel, QPushButton, QVBoxLayout, QHBoxLayout
from PyQt5.QtCore import Qt, QTimer
from neighbor_effect_chart import NeighborEffectChart
from water_balance_chart import WaterBalanceChart

class DualChartsTestWindow(QMainWindow):
    def __init__(self):
        super().__init__()
        self.setWindowTitle("双图表集成测试 - 邻桶效应 + 注水/吹除平衡")
        # 设置大界面尺寸，模拟主程序
        self.setGeometry(50, 50, 1600, 1000)
        
        # 设置深色主题
        self.setStyleSheet("""
            QMainWindow {
                background-color: #1e1e1e;
                color: white;
            }
            QLabel {
                color: #00FFFF;
                font-size: 12px;
                padding: 5px;
            }
            QPushButton {
                background-color: #2d5a87;
                color: white;
                border: 2px solid #00FFFF;
                border-radius: 5px;
                padding: 8px 16px;
                font-size: 12px;
                font-weight: bold;
            }
            QPushButton:hover {
                background-color: #3d6a97;
            }
            QPushButton:pressed {
                background-color: #1d4a77;
            }
        """)
        
        # 创建中央部件
        central_widget = QWidget()
        self.setCentralWidget(central_widget)
        
        self.setup_interface()
        
    def setup_interface(self):
        """设置界面布局"""
        central_widget = self.centralWidget()
        
        # 创建标题
        title_label = QLabel("双图表集成测试 - 模拟主程序布局 (1600x1000)")
        title_label.setGeometry(10, 10, 1580, 30)
        title_label.setAlignment(Qt.AlignCenter)
        title_label.setStyleSheet("font-size: 18px; font-weight: bold; color: #00FF00; border: 2px solid #00FF00; padding: 5px;")
        title_label.setParent(central_widget)
        
        # 模拟左侧控制区域
        left_area = QLabel("左侧控制区域\n(0-650像素)")
        left_area.setGeometry(10, 50, 630, 400)
        left_area.setAlignment(Qt.AlignCenter)
        left_area.setStyleSheet("background-color: rgba(0, 20, 40, 0.3); border: 2px dashed #666; font-size: 16px;")
        left_area.setParent(central_widget)
        
        # 模拟groupBox结果区域
        groupbox_area = QLabel("DD FS顺序优化结果区域\n(650-1000像素)\n宽度: 350px, 高度: 400px")
        groupbox_area.setGeometry(650, 50, 350, 400)
        groupbox_area.setAlignment(Qt.AlignCenter)
        groupbox_area.setStyleSheet("background-color: rgba(0, 34, 68, 0.7); border: 2px solid #00FFFF; font-size: 14px;")
        groupbox_area.setParent(central_widget)
        
        # 创建邻桶效应图表 (右上)
        self.neighbor_chart = NeighborEffectChart()
        chart_x = 1020  # 650 + 350 + 20
        chart_y = 50
        chart_width = 500
        chart_height = 450
        self.neighbor_chart.setGeometry(chart_x, chart_y, chart_width, chart_height)
        self.neighbor_chart.setParent(central_widget)
        
        # 创建注水/吹除平衡图表 (右下)
        self.water_balance_chart = WaterBalanceChart()
        balance_x = 1020
        balance_y = 50 + 450 + 20  # = 520
        balance_width = 500
        balance_height = 400
        self.water_balance_chart.setGeometry(balance_x, balance_y, balance_width, balance_height)
        self.water_balance_chart.setParent(central_widget)
        
        # 创建控制按钮区域
        self.create_control_buttons(central_widget)
        
        # 显示布局信息
        self.create_layout_info(central_widget)
        
        print(f"双图表布局:")
        print(f"邻桶效应图表: ({chart_x}, {chart_y}, {chart_width}, {chart_height})")
        print(f"注水/吹除平衡图表: ({balance_x}, {balance_y}, {balance_width}, {balance_height})")
        print(f"总界面尺寸: 1600x1000")
        
    def create_control_buttons(self, parent):
        """创建控制按钮"""
        # 测试按钮1
        btn1 = QPushButton("测试1: 8发导弹序列")
        btn1.setGeometry(10, 470, 200, 40)
        btn1.clicked.connect(lambda: self.run_dual_test([1, 3, 5, 7, 2, 4, 6, 8], 8, "8发导弹测试"))
        btn1.setParent(parent)
        
        # 测试按钮2
        btn2 = QPushButton("测试2: 16发导弹序列")
        btn2.setGeometry(220, 470, 200, 40)
        btn2.clicked.connect(lambda: self.run_dual_test([1, 3, 5, 7, 9, 11, 13, 15, 2, 4, 6, 8, 10, 12, 14, 16], 16, "16发导弹测试"))
        btn2.setParent(parent)
        
        # 测试按钮3
        btn3 = QPushButton("测试3: 不规则序列")
        btn3.setGeometry(430, 470, 200, 40)
        btn3.clicked.connect(lambda: self.run_dual_test([1, 8, 3, 6, 2, 7, 4, 5], 8, "不规则序列测试"))
        btn3.setParent(parent)
        
        # 重置按钮
        reset_btn = QPushButton("重置所有图表")
        reset_btn.setGeometry(10, 520, 200, 40)
        reset_btn.clicked.connect(self.reset_all_charts)
        reset_btn.setStyleSheet("background-color: #8B4513; border: 2px solid #FF6347;")
        reset_btn.setParent(parent)
        
        # 停止动画按钮
        stop_btn = QPushButton("停止所有动画")
        stop_btn.setGeometry(220, 520, 200, 40)
        stop_btn.clicked.connect(self.stop_all_animations)
        stop_btn.setStyleSheet("background-color: #B22222; border: 2px solid #FF0000;")
        stop_btn.setParent(parent)
        
        # 同步测试按钮
        sync_btn = QPushButton("同步动画测试")
        sync_btn.setGeometry(430, 520, 200, 40)
        sync_btn.clicked.connect(self.test_synchronized_animation)
        sync_btn.setStyleSheet("background-color: #228B22; border: 2px solid #00FF00;")
        sync_btn.setParent(parent)
        
    def create_layout_info(self, parent):
        """创建布局信息显示"""
        layout_info = QLabel(f"""双图表布局信息:

界面总尺寸: 1600 × 1000 像素

左侧控制区域: (10, 50, 630, 400)
结果显示区域: (650, 50, 350, 400)

邻桶效应图表: (1020, 50, 500, 450)
- 功能: 显示导弹间相互影响
- 数学模型: 指数衰减 + 位置因子
- 动态效果: 按发射顺序逐步展示

注水/吹除平衡图表: (1020, 520, 500, 400)
- 功能: 显示潜艇平衡状态
- 数学模型: 重量影响 + 注水补偿
- 动态效果: 实时平衡状态监控

协同工作:
✅ 使用相同的发射序列数据
✅ 同步的动态更新时机
✅ 一致的视觉风格和主题
✅ 互补的功能分析视角""")
        layout_info.setGeometry(10, 580, 630, 350)
        layout_info.setAlignment(Qt.AlignLeft | Qt.AlignTop)
        layout_info.setStyleSheet("background-color: rgba(20, 20, 20, 0.8); border: 1px solid #00FFFF; font-family: monospace; font-size: 10px; padding: 10px;")
        layout_info.setParent(parent)
        
        # 状态显示
        self.status_label = QLabel("状态: 等待测试")
        self.status_label.setGeometry(10, 950, 630, 30)
        self.status_label.setStyleSheet("background-color: rgba(0, 40, 0, 0.7); border: 2px solid #00FF00; padding: 5px; color: #00FF00; font-size: 14px; font-weight: bold;")
        self.status_label.setParent(parent)
        
    def run_dual_test(self, sequence, missile_count, test_name):
        """运行双图表测试"""
        self.status_label.setText(f"状态: 正在运行 - {test_name}")
        self.status_label.setStyleSheet("background-color: rgba(40, 40, 0, 0.7); border: 2px solid #FFFF00; padding: 5px; color: #FFFF00; font-size: 14px; font-weight: bold;")
        
        print(f"\n=== {test_name} ===")
        print(f"发射序列: {sequence}")
        print(f"导弹总数: {missile_count}")
        
        # 同时更新两个图表
        self.neighbor_chart.update_chart(sequence, missile_count)
        self.water_balance_chart.update_chart(sequence, missile_count)
        
        print("双图表已同步更新")
        
    def reset_all_charts(self):
        """重置所有图表"""
        self.neighbor_chart.reset_chart()
        self.water_balance_chart.reset_chart()
        self.status_label.setText("状态: 所有图表已重置")
        self.status_label.setStyleSheet("background-color: rgba(0, 40, 0, 0.7); border: 2px solid #00FF00; padding: 5px; color: #00FF00; font-size: 14px; font-weight: bold;")
        print("所有图表已重置")
        
    def stop_all_animations(self):
        """停止所有动画"""
        self.neighbor_chart.stop_animation()
        self.water_balance_chart.stop_animation()
        self.status_label.setText("状态: 所有动画已停止")
        self.status_label.setStyleSheet("background-color: rgba(40, 0, 0, 0.7); border: 2px solid #FF0000; padding: 5px; color: #FF0000; font-size: 14px; font-weight: bold;")
        print("所有动画已停止")
        
    def test_synchronized_animation(self):
        """测试同步动画"""
        self.status_label.setText("状态: 同步动画测试中")
        self.status_label.setStyleSheet("background-color: rgba(0, 40, 40, 0.7); border: 2px solid #00FFFF; padding: 5px; color: #00FFFF; font-size: 14px; font-weight: bold;")
        
        # 使用特定序列测试同步效果
        sync_sequence = [1, 4, 2, 3, 6, 5, 8, 7]
        print(f"\n=== 同步动画测试 ===")
        print(f"同步序列: {sync_sequence}")
        
        # 同时启动两个图表的动画
        self.neighbor_chart.update_chart(sync_sequence, 8)
        self.water_balance_chart.update_chart(sync_sequence, 8)
        
        print("同步动画已启动")

def main():
    app = QApplication(sys.argv)
    app.setStyle('Fusion')
    
    window = DualChartsTestWindow()
    window.show()
    
    print("双图表集成测试程序启动")
    print("界面尺寸: 1600x1000")
    print("请点击按钮进行测试")
    
    sys.exit(app.exec_())

if __name__ == "__main__":
    main()
