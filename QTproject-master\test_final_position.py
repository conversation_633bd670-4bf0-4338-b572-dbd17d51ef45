#!/usr/bin/env python3
# -*- coding: utf-8 -*-

"""
最终位置修复验证测试
"""

import sys
from PyQt5.QtWidgets import QApplication, QMainWindow, QWidget, QLabel
from PyQt5.QtCore import Qt
from neighbor_effect_chart import NeighborEffectChart

class FinalPositionTest(QMainWindow):
    def __init__(self):
        super().__init__()
        self.setWindowTitle("最终位置修复验证 - 确保图表不超出界面")
        # 设置与主界面相同的尺寸
        self.setGeometry(100, 100, 1325, 852)
        
        # 设置深色主题
        self.setStyleSheet("""
            QMainWindow {
                background-color: #1e1e1e;
                color: white;
            }
            QLabel {
                color: #00FFFF;
                font-size: 11px;
                border: 1px solid #00FFFF;
                padding: 3px;
            }
        """)
        
        # 创建中央部件
        central_widget = QWidget()
        self.setCentralWidget(central_widget)
        
        # 创建标题
        title_label = QLabel("最终位置修复验证 - 新布局方案")
        title_label.setGeometry(10, 10, 1305, 25)
        title_label.setAlignment(Qt.AlignCenter)
        title_label.setStyleSheet("font-size: 14px; font-weight: bold;")
        title_label.setParent(central_widget)
        
        # 模拟左侧控制区域 (0-650)
        left_area = QLabel("左侧控制区域 (0-650)")
        left_area.setGeometry(10, 45, 630, 350)
        left_area.setAlignment(Qt.AlignCenter)
        left_area.setStyleSheet("background-color: rgba(0, 20, 40, 0.3); border: 2px dashed #666;")
        left_area.setParent(central_widget)
        
        # 模拟groupBox结果区域 - 新尺寸 (650-900)
        groupbox_area = QLabel("DD FS顺序优化结果\n(650-900)\n宽度: 250px")
        groupbox_area.setGeometry(650, 45, 250, 350)
        groupbox_area.setAlignment(Qt.AlignCenter)
        groupbox_area.setStyleSheet("background-color: rgba(0, 34, 68, 0.7); border: 2px solid #00FFFF;")
        groupbox_area.setParent(central_widget)
        
        # 创建邻桶效应图表 - 新位置计算
        self.neighbor_chart = NeighborEffectChart()
        # 使用与main.py相同的新位置计算
        chart_x = 650 + 250 + 15  # = 915
        chart_width = 1325 - chart_x - 25  # = 385
        chart_width = min(chart_width, 300)  # = 300
        
        print(f"图表位置计算:")
        print(f"chart_x = 650 + 250 + 15 = {chart_x}")
        print(f"chart_width = 1325 - {chart_x} - 25 = {1325 - chart_x - 25}")
        print(f"实际宽度 = min({1325 - chart_x - 25}, 300) = {chart_width}")
        print(f"右边界 = {chart_x} + {chart_width} = {chart_x + chart_width}")
        print(f"界面宽度 = 1325")
        print(f"是否超出 = {chart_x + chart_width > 1325}")
        
        self.neighbor_chart.setGeometry(chart_x, 45, chart_width, 350)
        self.neighbor_chart.setParent(central_widget)
        
        # 显示图表位置信息
        chart_info = QLabel(f"邻桶效应图表\n位置: ({chart_x}, 45)\n尺寸: ({chart_width}, 350)\n右边界: {chart_x + chart_width}")
        chart_info.setGeometry(chart_x, 405, chart_width, 60)
        chart_info.setAlignment(Qt.AlignCenter)
        if chart_x + chart_width <= 1325:
            chart_info.setStyleSheet("background-color: rgba(0, 68, 34, 0.7); border: 2px solid #00FF00; color: #00FF00;")
        else:
            chart_info.setStyleSheet("background-color: rgba(68, 0, 0, 0.7); border: 2px solid #FF0000; color: #FF0000;")
        chart_info.setParent(central_widget)
        
        # 显示界面边界检查
        boundary_info = QLabel(f"""新布局方案验证:
左侧控制: 0-650 (650px)
结果显示: 650-900 (250px) 
图表区域: 915-1215 (300px)
右边界: {chart_x + chart_width} <= 1325? {'✅ 是' if chart_x + chart_width <= 1325 else '❌ 否'}
剩余空间: {1325 - (chart_x + chart_width)}px""")
        boundary_info.setGeometry(10, 405, 630, 100)
        boundary_info.setAlignment(Qt.AlignLeft | Qt.AlignTop)
        if chart_x + chart_width <= 1325:
            boundary_info.setStyleSheet("background-color: rgba(0, 68, 0, 0.7); border: 2px solid #00FF00; color: #00FF00; font-family: monospace;")
        else:
            boundary_info.setStyleSheet("background-color: rgba(68, 0, 0, 0.7); border: 2px solid #FF0000; color: #FF0000; font-family: monospace;")
        boundary_info.setParent(central_widget)
        
        # 添加测试数据到图表
        test_sequence = [1, 3, 5, 7, 2, 4, 6, 8]
        self.neighbor_chart.update_chart(test_sequence, 8)
        
        # 显示界面边界线
        boundary_line = QLabel("")
        boundary_line.setGeometry(1320, 0, 5, 852)
        boundary_line.setStyleSheet("background-color: red; border: none;")
        boundary_line.setParent(central_widget)
        
        boundary_label = QLabel("界面边界\n1325px")
        boundary_label.setGeometry(1270, 400, 50, 40)
        boundary_label.setAlignment(Qt.AlignCenter)
        boundary_label.setStyleSheet("background-color: red; color: white; font-size: 9px; border: none;")
        boundary_label.setParent(central_widget)

def main():
    app = QApplication(sys.argv)
    app.setStyle('Fusion')
    
    window = FinalPositionTest()
    window.show()
    
    sys.exit(app.exec_())

if __name__ == "__main__":
    main()
