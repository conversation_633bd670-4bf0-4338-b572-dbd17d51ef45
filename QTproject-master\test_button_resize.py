#!/usr/bin/env python3
# -*- coding: utf-8 -*-

"""
测试按钮缩小左移后的布局
解决注水/吹除平衡图表覆盖按钮问题
"""

import sys
from PyQt5.QtWidgets import QApplication, QMainWindow, QWidget, QLabel, QPushButton
from PyQt5.QtCore import Qt, QTimer, QDateTime
from neighbor_effect_chart import NeighborEffectChart
from water_balance_chart import WaterBalanceChart

class ButtonResizeTestWindow(QMainWindow):
    def __init__(self):
        super().__init__()
        self.setWindowTitle("按钮缩小左移测试 - 解决图表覆盖问题")
        # 设置1600x1000界面尺寸
        self.setGeometry(50, 50, 1600, 1000)
        
        # 设置深色主题
        self.setStyleSheet("""
            QMainWindow {
                background-color: #1e1e1e;
                color: white;
            }
            QLabel {
                color: #00FFFF;
                font-size: 12px;
                padding: 5px;
            }
            QPushButton {
                background-color: #2d5a87;
                color: white;
                border: 2px solid #00FFFF;
                border-radius: 5px;
                padding: 8px 16px;
                font-size: 12px;
                font-weight: bold;
            }
        """)
        
        # 创建中央部件
        central_widget = QWidget()
        self.setCentralWidget(central_widget)
        
        self.setup_resized_layout()
        
        # 设置时间更新定时器
        self.timer = QTimer()
        self.timer.timeout.connect(self.update_time)
        self.timer.start(1000)  # 每秒更新一次
        
    def setup_resized_layout(self):
        """设置按钮缩小左移后的布局"""
        central_widget = self.centralWidget()
        
        # 创建标题
        title_label = QLabel("按钮缩小左移测试 - 解决图表覆盖问题")
        title_label.setGeometry(10, 35, 1580, 30)
        title_label.setAlignment(Qt.AlignCenter)
        title_label.setStyleSheet("font-size: 18px; font-weight: bold; color: #00FF00; border: 2px solid #00FF00; padding: 5px;")
        title_label.setParent(central_widget)
        
        # 创建时间显示标签
        self.time_label = QLabel()
        self.time_label.setGeometry(400, 5, 400, 25)
        self.time_label.setAlignment(Qt.AlignCenter)
        self.time_label.setStyleSheet("background-color: rgba(0, 0, 0, 0.7); border: 1px solid #00FFFF; color: #00FFFF; font-size: 14px; font-weight: bold;")
        self.time_label.setParent(central_widget)
        self.update_time()
        
        # 创建窗口控制按钮
        self.create_window_buttons(central_widget)
        
        # 模拟左侧控制区域
        left_area = QLabel("左侧控制区域\n(0-570像素)")
        left_area.setGeometry(10, 75, 560, 400)
        left_area.setAlignment(Qt.AlignCenter)
        left_area.setStyleSheet("background-color: rgba(0, 20, 40, 0.3); border: 2px dashed #666; font-size: 16px;")
        left_area.setParent(central_widget)
        
        # 模拟groupBox结果区域 (缩小)
        groupbox_area = QLabel("DD FS顺序优化结果区域\n(570-720像素)\n宽度: 150px")
        groupbox_area.setGeometry(570, 75, 150, 400)
        groupbox_area.setAlignment(Qt.AlignCenter)
        groupbox_area.setStyleSheet("background-color: rgba(0, 34, 68, 0.7); border: 2px solid #00FFFF; font-size: 12px;")
        groupbox_area.setParent(central_widget)
        
        # 创建缩小左移的按钮
        self.create_resized_buttons(central_widget)
        
        # 创建邻桶效应图表 (右上角)
        self.neighbor_chart = NeighborEffectChart()
        neighbor_x = 1020
        neighbor_y = 75
        neighbor_width = 500
        neighbor_height = 200
        self.neighbor_chart.setGeometry(neighbor_x, neighbor_y, neighbor_width, neighbor_height)
        self.neighbor_chart.setParent(central_widget)
        
        # 创建注水/吹除平衡图表 (按钮右边)
        self.water_balance_chart = WaterBalanceChart()
        balance_x = 750   # 按钮右边 + 30像素间距
        balance_y = 290   # 与操作按钮对齐
        balance_width = 260  # 适当宽度
        balance_height = 410  # 覆盖三个按钮的高度范围
        self.water_balance_chart.setGeometry(balance_x, balance_y, balance_width, balance_height)
        self.water_balance_chart.setParent(central_widget)
        
        # 创建布局信息显示
        self.create_layout_info(central_widget)
        
        # 创建测试按钮
        self.create_test_buttons(central_widget)
        
        print(f"按钮缩小左移后布局:")
        print(f"界面尺寸: 1600x1000")
        print(f"操作按钮: (580, 290, 140, 70) - 缩小并左移")
        print(f"初始化按钮: (580, 460, 140, 70) - 缩小并左移")
        print(f"确定按钮: (580, 630, 140, 70) - 缩小并左移")
        print(f"邻桶效应图表: ({neighbor_x}, {neighbor_y}, {neighbor_width}, {neighbor_height}) - 右上角")
        print(f"注水/吹除平衡图表: ({balance_x}, {balance_y}, {balance_width}, {balance_height}) - 按钮右边")
        
    def create_window_buttons(self, parent):
        """创建窗口控制按钮"""
        # 最小化按钮
        minimize_btn = QPushButton("—")
        minimize_btn.setGeometry(1540, 0, 30, 20)
        minimize_btn.setStyleSheet("background-color: #666; color: white; border: 1px solid #999; font-size: 14px; font-weight: bold;")
        minimize_btn.clicked.connect(self.showMinimized)
        minimize_btn.setParent(parent)
        
        # 关闭按钮
        close_btn = QPushButton("×")
        close_btn.setGeometry(1570, 0, 30, 20)
        close_btn.setStyleSheet("background-color: #e81123; color: white; border: 1px solid #999; font-size: 14px; font-weight: bold;")
        close_btn.clicked.connect(self.close)
        close_btn.setParent(parent)
        
    def create_resized_buttons(self, parent):
        """创建缩小左移的控制按钮"""
        # 操作按钮 (缩小并左移)
        operate_btn = QPushButton("操作")
        operate_btn.setGeometry(580, 290, 140, 70)
        operate_btn.setStyleSheet("background-color: #4CAF50; font-size: 14px; font-weight: bold;")
        operate_btn.setParent(parent)
        
        # 初始化按钮 (缩小并左移)
        init_btn = QPushButton("初始化")
        init_btn.setGeometry(580, 460, 140, 70)
        init_btn.setStyleSheet("background-color: #FF9800; font-size: 14px; font-weight: bold;")
        init_btn.setParent(parent)
        
        # 确定按钮 (缩小并左移)
        certain_btn = QPushButton("确定")
        certain_btn.setGeometry(580, 630, 140, 70)
        certain_btn.setStyleSheet("background-color: #2196F3; font-size: 14px; font-weight: bold;")
        certain_btn.setParent(parent)
        
        # 按钮区域标注
        button_area = QLabel("缩小左移按钮区域\n(580-720像素)\n宽度: 140px\n高度: 70px")
        button_area.setGeometry(580, 220, 140, 60)
        button_area.setAlignment(Qt.AlignCenter)
        button_area.setStyleSheet("background-color: rgba(255, 255, 0, 0.2); border: 1px dashed #FFFF00; font-size: 9px; color: #FFFF00;")
        button_area.setParent(parent)
        
        # 图表区域标注
        chart_area = QLabel("注水/吹除平衡图表\n(750-1010像素)\n宽度: 260px\n不覆盖按钮")
        chart_area.setGeometry(750, 220, 260, 60)
        chart_area.setAlignment(Qt.AlignCenter)
        chart_area.setStyleSheet("background-color: rgba(0, 255, 0, 0.2); border: 1px dashed #00FF00; font-size: 9px; color: #00FF00;")
        chart_area.setParent(parent)
        
    def create_layout_info(self, parent):
        """创建布局信息显示"""
        layout_info = QLabel(f"""按钮缩小左移方案:

界面总尺寸: 1600 × 1000 像素

按钮修改:
原位置: (720, 290-630, 180, 80)
新位置: (580, 290-630, 140, 70)
改进: 宽度减少40px，左移140px，高度减少10px

图表重新定位:
注水/吹除平衡图表:
原位置: (1020, 285, 500, 400) - 可能与邻桶图表重叠
新位置: (750, 290, 260, 410) - 按钮右边独立空间

空间分配:
• 左侧控制区域: (10, 75, 560, 400)
• 结果显示区域: (570, 75, 150, 400) - 缩小
• 控制按钮区域: (580, 290-700, 140, 70×3) - 缩小左移
• 注水平衡图表: (750, 290, 260, 410) - 按钮右边
• 邻桶效应图表: (1020, 75, 500, 200) - 右上角

布局优势:
✅ 按钮完全可见，不被图表覆盖
✅ 注水图表有独立显示空间
✅ 邻桶效应图表保持在右上角
✅ 所有组件都有合理的显示空间
✅ 界面布局更加紧凑高效""")
        layout_info.setGeometry(10, 490, 560, 400)
        layout_info.setAlignment(Qt.AlignLeft | Qt.AlignTop)
        layout_info.setStyleSheet("background-color: rgba(20, 20, 20, 0.8); border: 1px solid #00FFFF; font-family: monospace; font-size: 10px; padding: 10px;")
        layout_info.setParent(parent)
        
        # 状态显示
        self.status_label = QLabel("状态: 按钮缩小左移测试就绪")
        self.status_label.setGeometry(10, 920, 560, 30)
        self.status_label.setStyleSheet("background-color: rgba(0, 40, 0, 0.7); border: 2px solid #00FF00; padding: 5px; color: #00FF00; font-size: 14px; font-weight: bold;")
        self.status_label.setParent(parent)
        
    def create_test_buttons(self, parent):
        """创建测试按钮"""
        # 测试按钮1
        test_btn1 = QPushButton("测试8发导弹")
        test_btn1.setGeometry(750, 720, 120, 40)
        test_btn1.clicked.connect(lambda: self.run_test([1, 3, 5, 7, 2, 4, 6, 8], 8, "8发导弹测试"))
        test_btn1.setParent(parent)
        
        # 测试按钮2
        test_btn2 = QPushButton("测试16发导弹")
        test_btn2.setGeometry(880, 720, 120, 40)
        test_btn2.clicked.connect(lambda: self.run_test([1, 3, 5, 7, 9, 11, 13, 15, 2, 4, 6, 8, 10, 12, 14, 16], 16, "16发导弹测试"))
        test_btn2.setParent(parent)
        
        # 重置按钮
        reset_btn = QPushButton("重置图表")
        reset_btn.setGeometry(750, 770, 120, 40)
        reset_btn.clicked.connect(self.reset_charts)
        reset_btn.setStyleSheet("background-color: #8B4513; border: 2px solid #FF6347;")
        reset_btn.setParent(parent)
        
        # 验证按钮
        verify_btn = QPushButton("验证布局")
        verify_btn.setGeometry(880, 770, 120, 40)
        verify_btn.clicked.connect(self.verify_layout)
        verify_btn.setStyleSheet("background-color: #228B22; border: 2px solid #00FF00;")
        verify_btn.setParent(parent)
        
    def update_time(self):
        """更新时间显示"""
        current_time = QDateTime.currentDateTime().toString("yyyy-MM-dd hh:mm:ss")
        self.time_label.setText(f"当前时间: {current_time}")
        
    def run_test(self, sequence, missile_count, test_name):
        """运行测试"""
        self.status_label.setText(f"状态: 正在运行 - {test_name}")
        self.status_label.setStyleSheet("background-color: rgba(40, 40, 0, 0.7); border: 2px solid #FFFF00; padding: 5px; color: #FFFF00; font-size: 14px; font-weight: bold;")
        
        print(f"\n=== {test_name} ===")
        print(f"发射序列: {sequence}")
        print(f"导弹总数: {missile_count}")
        
        # 同时更新两个图表
        self.neighbor_chart.update_chart(sequence, missile_count)
        self.water_balance_chart.update_chart(sequence, missile_count)
        
        print("双图表已同步更新，按钮不被覆盖")
        
    def reset_charts(self):
        """重置图表"""
        self.neighbor_chart.reset_chart()
        self.water_balance_chart.reset_chart()
        self.status_label.setText("状态: 图表已重置")
        self.status_label.setStyleSheet("background-color: rgba(0, 40, 0, 0.7); border: 2px solid #00FF00; padding: 5px; color: #00FF00; font-size: 14px; font-weight: bold;")
        print("图表已重置")
        
    def verify_layout(self):
        """验证布局"""
        self.status_label.setText("状态: 布局验证完成 - 按钮不被覆盖")
        self.status_label.setStyleSheet("background-color: rgba(0, 40, 40, 0.7); border: 2px solid #00FFFF; padding: 5px; color: #00FFFF; font-size: 14px; font-weight: bold;")
        
        print("\n=== 布局验证 ===")
        print("✅ 界面尺寸: 1600x1000")
        print("✅ 按钮缩小并左移: 腾出空间成功")
        print("✅ 操作按钮: (580, 290, 140, 70) - 不被覆盖")
        print("✅ 初始化按钮: (580, 460, 140, 70) - 不被覆盖")
        print("✅ 确定按钮: (580, 630, 140, 70) - 不被覆盖")
        print("✅ 邻桶效应图表: 右上角完整显示")
        print("✅ 注水/吹除平衡图表: 按钮右边完整显示")
        print("✅ 所有组件不重叠，布局合理")

def main():
    app = QApplication(sys.argv)
    app.setStyle('Fusion')
    
    window = ButtonResizeTestWindow()
    window.show()
    
    print("按钮缩小左移测试程序启动")
    print("验证图表覆盖问题的解决效果")
    
    sys.exit(app.exec_())

if __name__ == "__main__":
    main()
