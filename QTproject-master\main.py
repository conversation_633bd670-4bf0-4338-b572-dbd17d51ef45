"""

初始化的逻辑待完善
确定的逻辑待完善

# pyrcc5 转换 .qrc 文件:

# pyrcc5 -o v1/resource.py v1/resource.qrc

"""
import re
import time
import os
from PyQt5.QtGui import QPen, QBrush, QPainter, QIntValidator, QDoubleValidator, QPixmap, QIcon, QImage, \
    QRegExpValidator, QRegularExpressionValidator, QColor, QLinearGradient
from tsettings_dialog import TSettingsDialog  # 对话框类
import Dataset
import sys
from PyQt5.QtWidgets import QApplication, QMainWindow, QFileDialog, QTableWidgetItem, QMessageBox, QWidget, QLineEdit, \
    QGroupBox, QCheckBox, QHBoxLayout, QLabel, QFrame
from PyQt5 import QtCore
from PyQt5 import uic
import resource
# from res import image_rc
from PyQt5.QtWidgets import QButtonGroup
# from PyQt5.QtWidgets import QGraphicsDropShadowEffect
from PyQt5.QtCore import Qt, QTimer, QRegExp, QRegularExpression, QRect, QThread, pyqtSignal
import csv
import pandas as pd
from selection_dialog import SelectionDialog
from health_score_dialog import HealthScoreDialog
from dd_manager import DDManager
from priority_dialog import PriorityDialog
from dd_data_manager import DDDataManager  # 类名修改避免冲突
from CBhealth_score_dialog import CBHealthScoreDialog
from ship_data_manager import ShipDataManager
from resource_dialog import ResourceDialog
from resource_data_manager import ResourceDataManager

# 添加一个训练模型的工作线程类
class TrainModelThread(QThread):
    # 定义信号
    training_finished = pyqtSignal(bool)  # 训练完成信号，参数为是否成功
    training_progress = pyqtSignal(str)   # 训练进度信号，参数为进度消息
    
    def __init__(self, tdistance_path, columns_count):
        super().__init__()
        self.tdistance_path = tdistance_path
        self.columns_count = columns_count
        self.success = False
        
    def run(self):
        """线程执行函数，训练DQN模型"""
        try:
            # 发送开始训练的信号
            self.training_progress.emit("正在训练模型，请稍候...")
            
            # 导入DQN算法函数
            import sys
            import os
            
            # 获取missile_launch_rl.py所在路径
            missile_rl_path = os.path.dirname(os.path.abspath(__file__))  # 当前文件夹
            missile_rl_path = os.path.join(os.path.dirname(missile_rl_path), "missile_launch_rl.py")
            
            if not os.path.exists(missile_rl_path):
                # 如果找不到默认路径，尝试其他可能的路径
                possible_paths = [
                    r"C:\Users\<USER>\Desktop\武汉719\missile_launch_rl.py",  # 尝试父目录
                    r"missile_launch_rl.py"  # 尝试相对路径
                ]
                
                for path in possible_paths:
                    if os.path.exists(path):
                        missile_rl_path = path
                        break
            
            # 如果文件不存在，发送错误信号
            if not os.path.exists(missile_rl_path):
                self.training_progress.emit("找不到DQN算法文件，请确保missile_launch_rl.py在正确的位置")
                self.training_finished.emit(False)
                return
                
            # 导入DQN算法
            sys.path.append(os.path.dirname(missile_rl_path))
            
            try:
                from missile_launch_rl import get_optimal_sequence
                
                # 检查文件是否存在
                if not os.path.exists(self.tdistance_path):
                    self.training_progress.emit(f"找不到T距文件: {self.tdistance_path}")
                    self.training_finished.emit(False)
                    return
                
                # 调用DQN算法训练模型，传递first_missile_id=None表示仅训练模型，verbose=False以隐藏控制台输出
                get_optimal_sequence(self.tdistance_path, self.columns_count, first_missile_id=None, verbose=False)
                
                # 训练完成，发送成功信号
                self.training_progress.emit("模型训练完成，可以选择首选DD编号进行计算")
                self.success = True
                self.training_finished.emit(True)
                
            except ImportError as e:
                self.training_progress.emit(f"导入DQN算法失败: {e}")
                self.training_finished.emit(False)
            except Exception as e:
                self.training_progress.emit(f"训练模型时出错: {e}")
                self.training_finished.emit(False)
                import traceback
                traceback.print_exc()
                
        except Exception as e:
            self.training_progress.emit(f"调用DQN算法失败: {e}")
            self.training_finished.emit(False)
            import traceback
            traceback.print_exc()

# 添加一个计算最优发射序列的工作线程类
class CalculateSequenceThread(QThread):
    # 定义信号
    calculation_finished = pyqtSignal(bool, list, float)  # 计算完成信号，参数为是否成功、序列和评分
    calculation_progress = pyqtSignal(str)   # 计算进度信号，参数为进度消息
    
    def __init__(self, tdistance_path, columns_count, first_missile_id, excluded_missiles=None, launch_count=0):
        super().__init__()
        self.tdistance_path = tdistance_path
        self.columns_count = columns_count
        self.first_missile_id = first_missile_id
        self.excluded_missiles = excluded_missiles or []
        self.launch_count = launch_count
        self.success = False
        self.sequence = []
        self.score = 0.0
        
    def run(self):
        """线程执行函数，计算最优发射序列"""
        try:
            # 发送开始计算的信号
            first_missile_text = f"，首选DD: {self.first_missile_id}" if self.first_missile_id else ""
            excluded_text = f"，去选DD: {self.excluded_missiles}" if self.excluded_missiles else ""
            launch_text = f"，共打: {self.launch_count}发" if self.launch_count > 0 else ""
            self.calculation_progress.emit(f"正在计算最优FS序列，请稍候...{first_missile_text}{excluded_text}{launch_text}")
            
            # 导入DQN算法函数
            import sys
            import os
            
            # 获取missile_launch_rl.py所在路径
            missile_rl_path = os.path.dirname(os.path.abspath(__file__))  # 当前文件夹
            missile_rl_path = os.path.join(os.path.dirname(missile_rl_path), "missile_launch_rl.py")
            
            if not os.path.exists(missile_rl_path):
                # 如果找不到默认路径，尝试其他可能的路径
                possible_paths = [
                    r"C:\Users\<USER>\Desktop\武汉719\missile_launch_rl.py",  # 尝试父目录
                    r"missile_launch_rl.py"  # 尝试相对路径
                ]
                
                for path in possible_paths:
                    if os.path.exists(path):
                        missile_rl_path = path
                        break
            
            # 如果文件不存在，发送错误信号
            if not os.path.exists(missile_rl_path):
                self.calculation_progress.emit("找不到DQN算法文件，请确保missile_launch_rl.py在正确的位置")
                self.calculation_finished.emit(False, [], 0.0)
                return
                
            # 导入DQN算法
            sys.path.append(os.path.dirname(missile_rl_path))
            
            try:
                from missile_launch_rl import get_optimal_sequence
                
                # 检查文件是否存在
                if not os.path.exists(self.tdistance_path):
                    self.calculation_progress.emit(f"找不到T距文件: {self.tdistance_path}")
                    self.calculation_finished.emit(False, [], 0.0)
                    return
                
                # 调用DQN算法计算最优发射序列
                self.sequence, self.score = get_optimal_sequence(
                    self.tdistance_path, 
                    self.columns_count, 
                    self.first_missile_id, 
                    verbose=False,
                    excluded_missiles=self.excluded_missiles
                )
                
                # 如果设置了launch_count，则限制序列长度
                if self.launch_count > 0 and self.launch_count < len(self.sequence):
                    self.sequence = self.sequence[:self.launch_count]
                    # 重新计算得分
                    self.score = self.score * (self.launch_count / len(self.sequence))
                
                # 计算完成，发送成功信号
                self.calculation_progress.emit(f"最优FS序列计算完成！")
                self.success = True
                self.calculation_finished.emit(True, self.sequence, self.score)
                
            except ImportError as e:
                self.calculation_progress.emit(f"导入DQN算法失败: {e}")
                self.calculation_finished.emit(False, [], 0.0)
            except Exception as e:
                self.calculation_progress.emit(f"计算最优FS序列时出错: {e}")
                self.calculation_finished.emit(False, [], 0.0)
                import traceback
                traceback.print_exc()
                
        except Exception as e:
            self.calculation_progress.emit(f"调用DQN算法失败: {e}")
            self.calculation_finished.emit(False, [], 0.0)
            import traceback
            traceback.print_exc()

def is_positive_number(s):
    pattern = re.compile(r'^[+]?([0-9]*[.])?[0-9]+$')
    match = pattern.match(s)
    if match:
        return True
    else:
        return False


class MainWindow(QMainWindow):
    def __init__(self):
        super().__init__()
        self.setWindowFlag(QtCore.Qt.FramelessWindowHint)  # 隐藏窗口标题栏/无边框窗口
        # self.show()
        uic.loadUi('v1.ui', self)
        self.initUI()
        
        # 创建右侧结果显示区域和邻桶效应图表
        if hasattr(self, 'groupBox'):
            # 调整groupBox的位置和大小，适应1600x1000大界面
            # 界面总宽度1600，为图表预留更多空间
            # 按钮在(720,290,180,80)，所以groupBox可以更宽
            self.groupBox.setGeometry(650, 20, 350, 400)  # 增大尺寸以适应大界面
            # 设置标题为空，去除默认标题
            self.groupBox.setTitle("")

            # 移除原有的背景图像
            self.groupBox.setStyleSheet("""
                QGroupBox {
                    border: 2px solid #00FFFF;
                    border-radius: 5px;
                    background-color: rgba(0, 34, 68, 0.7);
                }
            """)
            self.groupBox.setTitle("")  # 清除标题

            # 创建结果显示区域的布局
            from PyQt5.QtWidgets import QVBoxLayout, QLabel, QTextBrowser, QHBoxLayout
            
            # 创建垂直布局，设置极小的边距以最大化内容区域
            result_layout = QVBoxLayout(self.groupBox)
            result_layout.setContentsMargins(5, 5, 5, 5)  # 设置极小的边距
            result_layout.setSpacing(2)  # 设置极小的控件间距
            
            # 添加标题标签，适当调整字体大小
            self.result_title = QLabel("DD FS顺序优化结果")
            self.result_title.setStyleSheet("""
                font-size: 15pt;
                font-weight: bold;
                color: #00FFFF;
                qproperty-alignment: 'AlignHCenter | AlignVCenter';
                padding: 0px;
                margin: 0px;
            """)
            result_layout.addWidget(self.result_title)
            
            # 添加最优FS顺序标签，适当调整字体大小
            self.fs_label = QLabel("最优FS顺序")
            self.fs_label.setStyleSheet("""
                color: white; 
                font-size: 12pt;
                padding: 0px;
                margin: 0px;
            """)
            result_layout.addWidget(self.fs_label)
            
            # 添加结果文本浏览器，调整字体大小
            self.result_browser = QTextBrowser()
            self.result_browser.setStyleSheet("""
                background-color: rgba(0, 20, 40, 0.8);
                color: white;
                font-size: 12pt;
                border: 1px solid #00FFFF;
                border-radius: 2px;
                padding: 5px;
                margin: 0px;
                line-height: 150%;
            """)
            self.result_browser.setMinimumHeight(180)  # 增加高度以充分利用空间
            # 设置自动换行模式，确保长文本能够正确显示
            self.result_browser.setLineWrapMode(QTextBrowser.WidgetWidth)
            result_layout.addWidget(self.result_browser)
            
            # 添加去选DD标签，调整字体大小
            self.excluded_label = QLabel("去选DD")
            self.excluded_label.setStyleSheet("""
                color: white; 
                font-size: 12pt;
                padding: 0px;
                margin: 0px;
            """)
            result_layout.addWidget(self.excluded_label)
            
            # 添加去选DD文本浏览器，调整字体大小
            self.excluded_browser = QTextBrowser()
            self.excluded_browser.setStyleSheet("""
                background-color: rgba(0, 20, 40, 0.8);
                color: white;
                font-size: 12pt;
                border: 1px solid #00FFFF;
                border-radius: 2px;
                padding: 5px;
                margin: 0px;
                line-height: 150%;
            """)
            self.excluded_browser.setMaximumHeight(60)  # 增加高度以显示更多内容
            # 设置自动换行模式，确保长文本能够正确显示
            self.excluded_browser.setLineWrapMode(QTextBrowser.WidgetWidth)
            result_layout.addWidget(self.excluded_browser)
            
            # 设置布局
            self.groupBox.setLayout(result_layout)

        # 创建邻桶效应图表
        self.create_neighbor_effect_chart()

        # 创建注水/吹除平衡图表
        self.create_water_balance_chart()

        # 设置原有结果显示区域样式（保留，以免影响其他功能）
        if hasattr(self, 'frame_9'):
            # 设置边框样式
            self.frame_9.setStyleSheet("""
                QFrame {
                    border: 2px solid #00FFFF;
                    border-radius: 5px;
                    background-color: rgba(0, 34, 68, 0.7);
                }
            """)
            
        # 设置结果标题样式
        if hasattr(self, 'label_10'):
            self.label_10.setText("DD FS顺序优化结果")
            self.label_10.setStyleSheet("""
                QLabel {
                    font-size: 16pt;
                    font-weight: bold;
                    color: #00FFFF;
                    qproperty-alignment: 'AlignHCenter | AlignVCenter';
                }
            """)
            
        # 设置文本浏览器样式
        if hasattr(self, 'textBrowser'):
            self.textBrowser.setStyleSheet("""
                QTextBrowser {
                    background-color: rgba(0, 20, 40, 0.8);
                    color: white;
                    font-size: 12pt;
                    border: 1px solid #00FFFF;
                    border-radius: 3px;
                }
            """)
            
        if hasattr(self, 'textBrowser_2'):
            self.textBrowser_2.setStyleSheet("""
                QTextBrowser {
                    background-color: rgba(0, 20, 40, 0.8);
                    color: white;
                    font-size: 12pt;
                    border: 1px solid #00FFFF;
                    border-radius: 3px;
                }
            """)

        # centralwidget作为鼠标移动窗口的区域
        self.centralwidget.mousePressEvent = self.centralWidget_mousePressEvent
        self.centralwidget.mouseMoveEvent = self.centralWidget_mouseMoveEvent
        # 初始化定时器
        self.timer = QTimer(self)
        self.timer.timeout.connect(self.update_time)
        self.timer.start(1000)  # 每秒更新一次

        # 设置时间标签的样式表，修改文字颜色为红色
        self.timeLabel.setStyleSheet("color: white;")
        self.timeLabel.setAlignment(Qt.AlignCenter)

        # 初始更新时间
        self.update_time()

        # 调整操作、初始化、确定按钮的位置，使它们与DD FS顺序优化结果保持对齐
        if hasattr(self, 'Operate'):
            self.Operate.setGeometry(720, 450, 241, 85)  # 向左下移动，与结果区域对齐
        if hasattr(self, 'Init'):
            self.Init.setGeometry(720, 560, 241, 85)  # 向左下移动，保持垂直间距
        if hasattr(self, 'Certain'):
            self.Certain.setGeometry(720, 670, 241, 85)  # 向左下移动，保持垂直间距

        # 主程序管理的全局变量
        self.global_input_double = 0  # 初始值，可被其他功能修改
        
        # 首选导弹ID，由showFirstMissileDialog设置
        self.first_missile_id = None
        
        # 初始化"共打"发射数量，默认为0（不限制）
        self.launch_count = 0
        # 设置输入验证器
        self.Fs_sum.setValidator(QIntValidator(0, 999, self))
        # 连接信号
        self.Fs_sum.textChanged.connect(self.validate_launch_count)
        
        # 移除首选导弹输入框初始化，因为现在使用Fs按钮替代
        
        # 设置时间和方向为只读
        # self.AirTime1.setReadOnly(True)
        # self.AirTime2.setReadOnly(True)
        # self.WaterTime1.setReadOnly(True)
        # self.WaterTime2.setReadOnly(True)
        # self.OPTime1.setReadOnly(True)
        # self.OPTime2.setReadOnly(True)

        # 初始时将 QWidget 内的所有组件设置为不可用
        self.set_widgets_enabled(self.Setparameter, False)
        # 绑定按钮点击事件"操作"
        self.Operate.clicked.connect(self.unlock_widgets)
        # 绑定按钮点击事件"确定"
        self.Certain.clicked.connect(self.lock_and_check)
        # 绑定按钮点击事件"初始化"
        self.Init.clicked.connect(self.init_display)
        # 绑定按钮点击事件"勾选"
        self.ControlAir.stateChanged.connect(self.update_groupbox_stateAir)
        # self.ControlWater.stateChanged.connect(self.update_groupbox_stateWater)
        # self.ControlOP.stateChanged.connect(self.update_groupbox_stateOP)
        # self.ControlDD.stateChanged.connect(self.update_groupbox_stateDD)
        self.ControlTube.stateChanged.connect(self.update_groupbox_stateTube)
        self.ControlEle.stateChanged.connect(self.update_groupbox_stateEle)
        self.ControlP.stateChanged.connect(self.update_groupbox_statePower)
        self.ControlNT.stateChanged.connect(self.update_groupbox_stateNT)

        # # 连接信号和槽，更新气源时间
        # self.connect_signalsAir()
        # 连接信号和槽，更新水源时间
        # self.connect_signalsWater()
        # 连接信号和槽，更新排水补水时间
        # self.connect_signalsOP()

        # 弹窗设置
        self.ControlTd.clicked.connect(self.show_settings_dialog)

        # 初始化参数
        self.valid_num  = 0
        self.Circle.paintEvent = self.custom_paint_event  # 重写原有widget的绘制方法

        # 设置输入验证器
        self.Sum.setValidator(QIntValidator(0, 24, self))

        # 连接信号
        self.Sum.textChanged.connect(self.validate_input)

        # DD健康分
        # 数据管理器
        self.dd_manager = DDManager()
        # 连接按钮事件
        self.Qx.clicked.connect(self.openSelectDialog)
        self.Health.clicked.connect(self.openHealthScoreDialog)

        #发射优先级 - 修改为首选导弹功能
        # 数据管理器
        self.dd_data_manager = DDDataManager()
        # 连接按钮事件 - 修改Fs按钮为首选导弹功能
        self.Fs.clicked.connect(self.showFirstMissileDialog)

        #船舶健康分
        # 数据管理器
        self.ship_data_manager = ShipDataManager()
        # 连接按钮事件
        self.CBj.clicked.connect(self.openCBHealthScoreDialog)

        #气源水源液压源选择
        # 数据管理器
        self.resource_data_manager = ResourceDataManager()

        # 连接按钮事件
        self.Szchoice.clicked.connect(self.openResourceDialog)

        # 设置图片大小
        pixmap = QPixmap("res\\szxz.png")
        if not pixmap.isNull():
            # 调整图片大小以适应按钮，使用 KeepAspectRatioByExpanding 保持纵横比并扩展填充
            scaled_pixmap = pixmap.scaled(self.Szchoice.size(), Qt.KeepAspectRatioByExpanding, Qt.SmoothTransformation)
            # 创建一个新的 QPixmap 用于裁剪，大小与按钮相同
            cropped_pixmap = QPixmap(self.Szchoice.size())
            cropped_pixmap.fill(Qt.transparent)  # 填充透明背景
            painter = QPainter(cropped_pixmap)
            # 计算绘制的起始位置，使图片居中
            x = (cropped_pixmap.width() - scaled_pixmap.width()) // 2
            y = (cropped_pixmap.height() - scaled_pixmap.height()) // 2
            painter.drawPixmap(x, y, scaled_pixmap)
            painter.end()

            icon = QIcon(cropped_pixmap)
            self.Szchoice.setIcon(icon)
            self.Szchoice.setIconSize(self.Szchoice.size())

            # 设置按钮样式，去除默认的边框和背景
            self.Szchoice.setStyleSheet("""
                                                            QPushButton {
                                                                border: none;
                                                                background-color: transparent;
                                                            }
                                                        """)


        # 设置图片大小
        pixmap = QPixmap("res\\szcb.png")
        if not pixmap.isNull():
            # 调整图片大小以适应按钮，使用 KeepAspectRatioByExpanding 保持纵横比并扩展填充
            scaled_pixmap = pixmap.scaled(self.CBj.size(), Qt.KeepAspectRatioByExpanding, Qt.SmoothTransformation)
            # 创建一个新的 QPixmap 用于裁剪，大小与按钮相同
            cropped_pixmap = QPixmap(self.CBj.size())
            cropped_pixmap.fill(Qt.transparent)  # 填充透明背景
            painter = QPainter(cropped_pixmap)
            # 计算绘制的起始位置，使图片居中
            x = (cropped_pixmap.width() - scaled_pixmap.width()) // 2
            y = (cropped_pixmap.height() - scaled_pixmap.height()) // 2
            painter.drawPixmap(x, y, scaled_pixmap)
            painter.end()

            icon = QIcon(cropped_pixmap)
            self.CBj.setIcon(icon)
            self.CBj.setIconSize(self.CBj.size())

            # 设置按钮样式，去除默认的边框和背景
            self.CBj.setStyleSheet("""
                                                    QPushButton {
                                                        border: none;
                                                        background-color: transparent;
                                                    }
                                                """)

        # 设置Fs按钮样式，去除默认的边框和背景
        self.Fs.setStyleSheet("""
                                    QPushButton {
                                        border: none;
                                        background-color: transparent;
                                    }
                                """)

        # 设置图片大小
        pixmap = QPixmap("res\\DDqx.png")
        if not pixmap.isNull():
            # 调整图片大小以适应按钮，使用 KeepAspectRatioByExpanding 保持纵横比并扩展填充
            scaled_pixmap = pixmap.scaled(self.Qx.size(), Qt.KeepAspectRatioByExpanding, Qt.SmoothTransformation)
            # 创建一个新的 QPixmap 用于裁剪，大小与按钮相同
            cropped_pixmap = QPixmap(self.Qx.size())
            cropped_pixmap.fill(Qt.transparent)  # 填充透明背景
            painter = QPainter(cropped_pixmap)
            # 计算绘制的起始位置，使图片居中
            x = (cropped_pixmap.width() - scaled_pixmap.width()) // 2
            y = (cropped_pixmap.height() - scaled_pixmap.height()) // 2
            painter.drawPixmap(x, y, scaled_pixmap)
            painter.end()

            icon = QIcon(cropped_pixmap)
            self.Qx.setIcon(icon)
            self.Qx.setIconSize(self.Qx.size())

            # 设置按钮样式，去除默认的边框和背景
            self.Qx.setStyleSheet("""
                                    QPushButton {
                                        border: none;
                                        background-color: transparent;
                                    }
                                """)

        # 设置图片大小
        pixmap = QPixmap("res\\Shealth.png")
        if not pixmap.isNull():
            # 调整图片大小以适应按钮，使用 KeepAspectRatioByExpanding 保持纵横比并扩展填充
            scaled_pixmap = pixmap.scaled(self.Health.size(), Qt.KeepAspectRatioByExpanding, Qt.SmoothTransformation)
            # 创建一个新的 QPixmap 用于裁剪，大小与按钮相同
            cropped_pixmap = QPixmap(self.Health.size())
            cropped_pixmap.fill(Qt.transparent)  # 填充透明背景
            painter = QPainter(cropped_pixmap)
            # 计算绘制的起始位置，使图片居中
            x = (cropped_pixmap.width() - scaled_pixmap.width()) // 2
            y = (cropped_pixmap.height() - scaled_pixmap.height()) // 2
            painter.drawPixmap(x, y, scaled_pixmap)
            painter.end()
            icon = QIcon(cropped_pixmap)
            self.Health.setIcon(icon)
            self.Health.setIconSize(self.Health.size())
            # 设置按钮样式，去除默认的边框和背景
            self.Health.setStyleSheet("""
                                            QPushButton {
                                                border: none;
                                                background-color: transparent;
                                            }
                                        """)


        #设置图片大小
        pixmap = QPixmap("res\\初始化1.png")
        if not pixmap.isNull():
            # 调整图片大小以适应按钮，使用 KeepAspectRatioByExpanding 保持纵横比并扩展填充
            scaled_pixmap = pixmap.scaled(self.Init.size(), Qt.KeepAspectRatioByExpanding, Qt.SmoothTransformation)
            # 创建一个新的 QPixmap 用于裁剪，大小与按钮相同
            cropped_pixmap = QPixmap(self.Init.size())
            cropped_pixmap.fill(Qt.transparent)  # 填充透明背景
            painter = QPainter(cropped_pixmap)
            # 计算绘制的起始位置，使图片居中
            x = (cropped_pixmap.width() - scaled_pixmap.width()) // 2
            y = (cropped_pixmap.height() - scaled_pixmap.height()) // 2
            painter.drawPixmap(x, y, scaled_pixmap)
            painter.end()

            icon = QIcon(cropped_pixmap)
            self.Init.setIcon(icon)
            self.Init.setIconSize(self.Init.size())

            # 设置按钮样式，去除默认的边框和背景
            self.Init.setStyleSheet("""
                            QPushButton {
                                border: none;
                                background-color: transparent;
                            }
                        """)

        pixmap = QPixmap("res\\确认.png")
        if not pixmap.isNull():
            # 调整图片大小以适应按钮，使用 KeepAspectRatioByExpanding 保持纵横比并扩展填充
            scaled_pixmap = pixmap.scaled(self.Certain.size(), Qt.KeepAspectRatioByExpanding, Qt.SmoothTransformation)
            # 创建一个新的 QPixmap 用于裁剪，大小与按钮相同
            cropped_pixmap = QPixmap(self.Certain.size())
            cropped_pixmap.fill(Qt.transparent)  # 填充透明背景
            painter = QPainter(cropped_pixmap)
            # 计算绘制的起始位置，使图片居中
            x = (cropped_pixmap.width() - scaled_pixmap.width()) // 2
            y = (cropped_pixmap.height() - scaled_pixmap.height()) // 2
            painter.drawPixmap(x, y, scaled_pixmap)
            painter.end()

            icon = QIcon(cropped_pixmap)
            self.Certain.setIcon(icon)
            self.Certain.setIconSize(self.Certain.size())

            # 设置按钮样式，去除默认的边框和背景
            self.Certain.setStyleSheet("""
                            QPushButton {
                                border: none;
                                background-color: transparent;
                            }
                        """)

        pixmap = QPixmap("res\\操作1.png")
        if not pixmap.isNull():
            # 调整图片大小以适应按钮，使用 KeepAspectRatioByExpanding 保持纵横比并扩展填充
            scaled_pixmap = pixmap.scaled(self.Operate.size(), Qt.KeepAspectRatioByExpanding, Qt.SmoothTransformation)
            # 创建一个新的 QPixmap 用于裁剪，大小与按钮相同
            cropped_pixmap = QPixmap(self.Operate.size())
            cropped_pixmap.fill(Qt.transparent)  # 填充透明背景
            painter = QPainter(cropped_pixmap)
            # 计算绘制的起始位置，使图片居中
            x = (cropped_pixmap.width() - scaled_pixmap.width()) // 2
            y = (cropped_pixmap.height() - scaled_pixmap.height()) // 2
            painter.drawPixmap(x, y, scaled_pixmap)
            painter.end()

            icon = QIcon(cropped_pixmap)
            self.Operate.setIcon(icon)
            self.Operate.setIconSize(self.Operate.size())

            # 设置按钮样式，去除默认的边框和背景
            self.Operate.setStyleSheet("""
                            QPushButton {
                                border: none;
                                background-color: transparent;
                            }
                        """)


        pixmap = QPixmap("res\\设置T距.png")
        if not pixmap.isNull():
            # 调整图片大小以适应按钮，使用 KeepAspectRatioByExpanding 保持纵横比并扩展填充
            scaled_pixmap = pixmap.scaled(self.ControlTd.size(), Qt.KeepAspectRatioByExpanding, Qt.SmoothTransformation)
            # 创建一个新的 QPixmap 用于裁剪，大小与按钮相同
            cropped_pixmap = QPixmap(self.ControlTd.size())
            cropped_pixmap.fill(Qt.transparent)  # 填充透明背景
            painter = QPainter(cropped_pixmap)
            # 计算绘制的起始位置，使图片居中
            x = (cropped_pixmap.width() - scaled_pixmap.width()) // 2
            y = (cropped_pixmap.height() - scaled_pixmap.height()) // 2
            painter.drawPixmap(x, y, scaled_pixmap)
            painter.end()

            icon = QIcon(cropped_pixmap)
            self.ControlTd.setIcon(icon)
            self.ControlTd.setIconSize(self.ControlTd.size())

            # 设置按钮样式，去除默认的边框和背景
            self.ControlTd.setStyleSheet("""
                            QPushButton {
                                border: none;
                                background-color: transparent;
                            }
                        """)

        # 初始化"首选导弹"按钮的显示
        self.update_first_missile_button()
        
        # 使窗口置于最前面
        # self.setWindowFlags(self.windowFlags() | QtCore.Qt.WindowStaysOnTopHint)

    def update_first_missile_button(self):
        """更新首选DD按钮的显示"""
        # 创建一个新的QPixmap
        cropped_pixmap = QPixmap(self.Fs.size())
        cropped_pixmap.fill(Qt.transparent)  # 填充透明背景
        painter = QPainter(cropped_pixmap)
        
        # 绘制渐变背景
        gradient = QLinearGradient(0, 0, 0, cropped_pixmap.height())
        gradient.setColorAt(0, QColor(0, 30, 60))
        gradient.setColorAt(1, QColor(0, 10, 30))
        painter.setBrush(QBrush(gradient))
        painter.setPen(QPen(QColor(0, 255, 255), 2))  # 青色边框
        painter.drawRoundedRect(0, 0, cropped_pixmap.width(), cropped_pixmap.height(), 10, 10)
        
        # 设置字体
        font = painter.font()
        font.setPointSize(14)  # 增大字体
        font.setBold(True)
        painter.setFont(font)
        painter.setPen(QColor(0, 255, 255))  # 使用青色文字
        
        # 绘制"首选"文字
        text_rect_prefix = QRect(10, 0, 60, cropped_pixmap.height())
        painter.drawText(text_rect_prefix, Qt.AlignVCenter, "首选")
        
        # 绘制矩形框和选择的DD编号
        rect_width = 30
        rect_height = 30
        rect_x = 70
        rect_y = (cropped_pixmap.height() - rect_height) // 2
        
        painter.setPen(QPen(QColor(0, 255, 255), 2))
        painter.setBrush(QBrush(QColor(0, 0, 0, 100)))  # 半透明黑色背景
        painter.drawRect(rect_x, rect_y, rect_width, rect_height)
        
        # 如果已经选择了DD编号，则在矩形框中显示
        if self.first_missile_id is not None:
            painter.drawText(QRect(rect_x, rect_y, rect_width, rect_height), 
                            Qt.AlignCenter, str(self.first_missile_id))
        
        # 绘制"号"文字
        text_rect_suffix = QRect(rect_x + rect_width + 5, 0, 30, cropped_pixmap.height())
        painter.drawText(text_rect_suffix, Qt.AlignVCenter, "号")
        
        painter.end()
        
        # 更新按钮图标
        icon = QIcon(cropped_pixmap)
        self.Fs.setIcon(icon)
        self.Fs.setIconSize(self.Fs.size())
        
        # 设置工具提示
        tooltip = "设置首选FS的DD编号"
        if self.first_missile_id is not None:
            tooltip += f"（当前选择：{self.first_missile_id}号）"
        self.Fs.setToolTip(tooltip)

    def openResourceDialog(self):
        # 创建资源选择对话框
        dialog = ResourceDialog(self, self.global_input_double)
        if dialog.exec_():
            resources = dialog.getResources()
            if not resources:
                self.show_warning("未设置任何资源")
                return

            self.resource_data_manager.setResources(resources)
            result = self.resource_data_manager.saveToExcel()

            if result:
                self.show_info(f"资源数据已成功保存到 {result}")
            else:
                self.show_error("保存资源数据失败")

    def openCBHealthScoreDialog(self):
        # 创建健康分设置对话框
        dialog = CBHealthScoreDialog(self, self.global_input_double)
        if dialog.exec_():
            scores = dialog.getScores()
            if not scores:
                self.show_warning("未设置任何健康分")
                return

            self.ship_data_manager.setScores(scores)
            result = self.ship_data_manager.saveScoresToExcel()

            if result:
                self.show_info(f"船舶健康分已成功保存到 {result}")
            else:
                self.show_error("保存船舶健康分失败")

    def showFirstMissileDialog(self):
        """显示首选导弹设置对话框"""
        try:
            # 获取列数和导弹数量
            columns_count = 0
            if hasattr(self, 'Sum') and self.Sum.text():
                columns_count = int(self.Sum.text())
            else:
                # 如果界面上没有设置，使用默认值2
                columns_count = 2
                
            if columns_count <= 0 or columns_count > 16:
                self.show_warning("列数必须在1到16之间，使用默认值2")
                columns_count = 2
                
            missile_count = columns_count * 2  # 导弹数量 = 列数 * 2
            
            # 创建对话框
            from PyQt5.QtWidgets import QDialog, QVBoxLayout, QLabel, QComboBox, QPushButton
            
            dialog = QDialog(self)
            dialog.setWindowTitle("设置首选DD")
            dialog.setMinimumWidth(300)
            
            # 设置对话框背景颜色和样式
            dialog.setStyleSheet("background-color: #002244;")
            
            layout = QVBoxLayout()
            
            # 添加提示标签
            label = QLabel("请选择首选DD编号:")
            label.setStyleSheet("color: white; font-size: 12pt;")
            layout.addWidget(label)
            
            # 添加下拉框
            combo = QComboBox()
            combo.setStyleSheet("""
                QComboBox {
                    background-color: #001428;
                    color: white;
                    border: 1px solid #00FFFF;
                    padding: 5px;
                    min-height: 25px;
                }
                QComboBox::drop-down {
                    border: 0px;
                }
                QComboBox::down-arrow {
                    image: url(res/down_arrow.png);
                    width: 12px;
                    height: 12px;
                }
                QComboBox QAbstractItemView {
                    background-color: #001428;
                    color: white;
                    selection-background-color: #005588;
                }
            """)
            
            combo.addItem("无")  # 添加"无"选项
            for i in range(1, missile_count + 1):
                combo.addItem(str(i))
                
            # 如果已经设置了首选导弹，则默认选中
            if self.first_missile_id is not None:
                index = combo.findText(str(self.first_missile_id))
                if index >= 0:
                    combo.setCurrentIndex(index)
                    
            layout.addWidget(combo)
            
            # 添加确定和取消按钮
            button_layout = QHBoxLayout()
            ok_button = QPushButton("确定")
            ok_button.setStyleSheet("background-color: #005588; color: white;")
            cancel_button = QPushButton("取消")
            cancel_button.setStyleSheet("background-color: #005588; color: white;")
            
            button_layout.addWidget(ok_button)
            button_layout.addWidget(cancel_button)
            
            layout.addLayout(button_layout)
            
            dialog.setLayout(layout)
            
            # 连接按钮信号
            ok_button.clicked.connect(dialog.accept)
            cancel_button.clicked.connect(dialog.reject)
            
            # 显示对话框
            if dialog.exec_():
                # 用户点击了确定
                selected_text = combo.currentText()
                if selected_text != "无":
                    # 获取选择的导弹ID
                    selected_id = int(selected_text)
                    
                    # 检查选择的导弹是否在去选列表中
                    excluded_missiles = []
                    if hasattr(self, 'dd_manager') and self.dd_manager:
                        excluded_missiles = self.dd_manager.getSelectedDDs()
                    
                    if selected_id in excluded_missiles:
                        # 创建确认对话框
                        msg_box = QMessageBox()
                        msg_box.setWindowTitle("警告")
                        msg_box.setText(f"DD {selected_id} 已被去选，不能发射。")
                        msg_box.setInformativeText("是否重新选择首选DD？")
                        msg_box.setStandardButtons(QMessageBox.Ok | QMessageBox.Cancel)
                        msg_box.setDefaultButton(QMessageBox.Ok)
                        
                        # 设置按钮文本
                        msg_box.button(QMessageBox.Ok).setText("确定")
                        msg_box.button(QMessageBox.Cancel).setText("取消")
                        
                        # 设置消息框样式
                        msg_box.setStyleSheet("""
                            QMessageBox {
                                background-color: #002244;
                                color: white;
                            }
                            QLabel {
                                color: white;
                            }
                            QPushButton {
                                background-color: #005588;
                                color: white;
                                min-width: 80px;
                                min-height: 24px;
                            }
                        """)
                        
                        # 显示对话框
                        result = msg_box.exec_()
                        
                        if result == QMessageBox.Ok:
                            # 用户选择重新选择，递归调用本方法
                            self.showFirstMissileDialog()
                            return
                        # 否则用户选择取消，强制使用该导弹作为首选
                    
                    # 更新首选导弹ID
                    self.first_missile_id = selected_id
                    # 更新按钮显示
                    self.update_first_missile_button()
                    # 先弹出已设置首选导弹的提示
                    self.show_info(f"已设置首选DD: {selected_text}号")
                    # 然后调用DQN算法
                    self.run_dqn_algorithm()
                else:
                    # 用户选择了"无"，清除首选导弹设置
                    self.first_missile_id = None
                    # 更新按钮显示
                    self.update_first_missile_button()
                    self.show_info("已清除首选DD设置")
                        
        except ValueError as e:
            self.show_warning(f"无效的数值: {str(e)}")
        except Exception as e:
            self.show_error(f"设置首选DD时出错: {str(e)}")
            import traceback
            traceback.print_exc()

    def openSelectDialog(self):
        # 使用全局变量作为选项数量
        dialog = SelectionDialog(self, self.global_input_double)
        if dialog.exec_():
            selected_dds = dialog.getSelectedOptions()
            self.dd_manager.setSelectedDDs(selected_dds)
            self.show_info(f"已成功选择 {len(selected_dds)} 个DD")

    def openHealthScoreDialog(self):
        # 获取已选择的DD
        selected_dds = self.dd_manager.getSelectedDDs()

        # if not selected_dds:
        #     self.show_warning("请先选择DD")
        #     return

        # 使用全局变量作为选项数量
        dialog = HealthScoreDialog(self, self.global_input_double, selected_dds)
        if dialog.exec_():
            scores = dialog.getScores()
            self.dd_manager.setScores(scores)
            result = self.dd_manager.saveToExcel()

            if result:
                self.show_info(f"健康分已成功保存到 {result}")
            else:
                self.show_error("保存健康分失败")

    def show_info(self, message):
        """显示信息弹窗"""
        msg = QMessageBox(self)
        msg.setIcon(QMessageBox.Information)
        msg.setWindowTitle("信息")
        msg.setText(message)
        msg.setStandardButtons(QMessageBox.Ok)
        msg.button(QMessageBox.Ok).setText("确定")
        
        # 设置消息框样式
        msg.setStyleSheet("""
            QMessageBox {
                background-color: #002244;
                color: white;
            }
            QLabel {
                color: white;
            }
            QPushButton {
                background-color: #005588;
                color: white;
                min-width: 80px;
                min-height: 24px;
            }
        """)
        
        msg.exec_()

    def show_warning(self, message):
        """显示警告弹窗"""
        msg = QMessageBox(self)
        msg.setIcon(QMessageBox.Warning)
        msg.setWindowTitle("警告")
        msg.setText(message)
        msg.setStandardButtons(QMessageBox.Ok)
        msg.button(QMessageBox.Ok).setText("确定")
        
        # 设置消息框样式
        msg.setStyleSheet("""
            QMessageBox {
                background-color: #002244;
                color: white;
            }
            QLabel {
                color: white;
            }
            QPushButton {
                background-color: #005588;
                color: white;
                min-width: 80px;
                min-height: 24px;
            }
        """)
        
        msg.exec_()

    def show_error(self, message):
        """显示错误弹窗"""
        msg = QMessageBox(self)
        msg.setIcon(QMessageBox.Critical)
        msg.setWindowTitle("错误")
        msg.setText(message)
        msg.setStandardButtons(QMessageBox.Ok)
        msg.button(QMessageBox.Ok).setText("确定")
        
        # 设置消息框样式
        msg.setStyleSheet("""
            QMessageBox {
                background-color: #002244;
                color: white;
            }
            QLabel {
                color: white;
            }
            QPushButton {
                background-color: #005588;
                color: white;
                min-width: 80px;
                min-height: 24px;
            }
        """)
        
        msg.exec_()

    def show_settings_dialog(self):
        """显示设置对话框"""
        if self.valid_num < 4:
            QMessageBox.warning(self, "错误", "有效圆数量不足4个")
            return

        dialog = TSettingsDialog(self.valid_num, self)
        if dialog.exec_() == TSettingsDialog.Accepted:
            try:
                data = dialog.get_values()
                self.save_to_excel(data)
                QMessageBox.information(self, "成功", "数据已保存到Tdistance.xlsx")
                
                # 在T距设置完成后立即在后台训练模型
                self.train_dqn_model()
                
            except Exception as e:
                QMessageBox.critical(self, "错误", f"保存失败: {str(e)}")
                
    def train_dqn_model(self):
        """设置T距后在后台线程训练模型，不阻塞UI"""
        try:
            # 获取T距文件路径
            tdistance_path = os.path.join(os.path.dirname(os.path.abspath(__file__)), "Tdistance.xlsx")
            
            # 获取列数
            columns_count = 0
            if hasattr(self, 'Sum') and self.Sum.text():
                columns_count = int(self.Sum.text())
            else:
                # 如果界面上没有设置，使用默认值2
                columns_count = 2
                
            if columns_count <= 0 or columns_count > 16:
                self.show_warning("列数必须在1到16之间，使用默认值2")
                columns_count = 2
            
            # 显示训练中的提示
            self.textBrowser.setText("正在训练模型，请稍候...")
            
            # 创建并启动训练线程
            self.train_thread = TrainModelThread(tdistance_path, columns_count)
            
            # 连接信号
            self.train_thread.training_progress.connect(self.update_training_progress)
            self.train_thread.training_finished.connect(self.on_training_finished)
            
            # 启动线程
            self.train_thread.start()
                
        except Exception as e:
            self.show_error(f"启动模型训练失败: {e}")
            import traceback
            traceback.print_exc()
    
    def update_training_progress(self, message):
        """更新训练进度信息"""
        self.textBrowser.setText(message)
    
    def on_training_finished(self, success):
        """训练完成后的回调函数"""
        if success:
            self.textBrowser.setText("模型训练完成，可以选择首选DD编号进行计算")
        else:
            self.textBrowser.setText("模型训练失败，请检查日志获取详细信息")

    def run_dqn_algorithm(self):
        """运行DQN算法计算最优发射顺序，在后台线程中执行以不阻塞UI"""
        try:
            # 获取T距文件路径
            tdistance_path = os.path.join(os.path.dirname(os.path.abspath(__file__)), "Tdistance.xlsx")
            
            # 检查文件是否存在
            if not os.path.exists(tdistance_path):
                self.show_error(f"找不到T距文件: {tdistance_path}")
                return
            
            # 获取列数
            columns_count = 0
            if hasattr(self, 'Sum') and self.Sum.text():
                columns_count = int(self.Sum.text())
            else:
                # 如果界面上没有设置，使用默认值2
                columns_count = 2
                
            if columns_count <= 0 or columns_count > 16:
                self.show_warning("列数必须在1到16之间，使用默认值2")
                columns_count = 2
            
            # 获取首选导弹编号 - 使用实例变量
            first_missile_id = self.first_missile_id
            if first_missile_id is not None:
                max_missile_id = columns_count * 2  # 导弹数量 = 列数 * 2
                if first_missile_id < 1 or first_missile_id > max_missile_id:
                    self.show_warning(f"首选DD编号{first_missile_id}超出范围(1-{max_missile_id})，已忽略")
                    first_missile_id = None
                    self.first_missile_id = None
            
            # 确保first_missile_id不为None，因为我们只想使用已训练好的模型进行推理
            if first_missile_id is None:
                self.show_warning("请先设置首选DD编号")
                self.textBrowser.setText("请先设置首选DD编号")
                return
            
            # 获取被去选的导弹列表
            excluded_missiles = []
            if hasattr(self, 'dd_manager') and self.dd_manager:
                excluded_missiles = self.dd_manager.getSelectedDDs()
                
            # 获取共打发射数量
            launch_count = self.launch_count
                
            # 显示计算中的提示
            first_missile_text = f"，首选DD: {first_missile_id}" if first_missile_id else ""
            excluded_text = f"，去选DD: {excluded_missiles}" if excluded_missiles else ""
            launch_text = f"，共打: {launch_count}发" if launch_count > 0 else ""
            self.textBrowser.setText(f"正在计算最优FS序列，请稍候...{first_missile_text}{excluded_text}{launch_text}")
            self.textBrowser.repaint()  # 强制更新UI
            
            # 创建并启动计算线程
            self.calc_thread = CalculateSequenceThread(
                tdistance_path, 
                columns_count, 
                first_missile_id, 
                excluded_missiles=excluded_missiles,
                launch_count=launch_count
            )
            
            # 连接信号
            self.calc_thread.calculation_progress.connect(self.update_calculation_progress)
            self.calc_thread.calculation_finished.connect(self.on_calculation_finished)
            
            # 启动线程
            self.calc_thread.start()
                
        except Exception as e:
            self.show_error(f"启动计算失败: {e}")
            import traceback
            traceback.print_exc()
    
    def update_calculation_progress(self, message):
        """更新计算进度信息"""
        self.textBrowser.setText(message)
    
    def on_calculation_finished(self, success, sequence, score):
        """计算完成后的回调函数"""
        if not success:
            if hasattr(self, 'textBrowser'):
                self.textBrowser.setText("计算最优FS序列失败，请检查日志获取详细信息")
            if hasattr(self, 'result_browser'):
                self.result_browser.setText("计算最优FS序列失败，请检查日志获取详细信息")
            return
            
        # 显示结果 - 原始格式（用于原有位置）
        result_text = f"最优FS序列计算完成！\n\n"
        columns_count = 0
        if hasattr(self, 'Sum') and self.Sum.text():
            columns_count = int(self.Sum.text())
        else:
            columns_count = 2
            
        result_text += f"DD数量: {columns_count * 2}\n"
        if self.first_missile_id:
            result_text += f"首选DD: {self.first_missile_id}\n"
        
        # 添加共打发射数量信息
        if self.launch_count > 0:
            result_text += f"共打: {self.launch_count}发\n"
            
        # 添加去选DD信息
        excluded_missiles = []
        if hasattr(self, 'dd_manager') and self.dd_manager:
            excluded_missiles = self.dd_manager.getSelectedDDs()
        if excluded_missiles:
            result_text += f"DD去选: {excluded_missiles}\n"
            
        result_text += f"最优FS序列: {sequence}\n"
        result_text += f"序列评分: {score:.4f}\n"
        
        # 将结果显示在界面上的textBrowser中（原有位置）
        if hasattr(self, 'textBrowser'):
            self.textBrowser.setText(result_text)
        
        # 如果有无法FS的DD，显示在textBrowser_2中（原有位置）
        if hasattr(self, 'textBrowser_2'):
            if excluded_missiles:
                self.textBrowser_2.setText(f"去选DD: {excluded_missiles}")
            else:
                self.textBrowser_2.setText("无去选DD")
        
        # 为右侧结果区域创建格式，增加适当行距
        compact_result = f"DD数量:{columns_count * 2}"
        
        # 添加首选DD信息
        if self.first_missile_id:
            compact_result += f" 首选DD:{self.first_missile_id}\n\n"
        else:
            compact_result += f"\n\n"
            
        # 共打信息单独放在一行
        if self.launch_count > 0:
            compact_result += f"共打:{self.launch_count}发\n\n"
        
        # 优化序列显示格式，使其更易读
        sequence_str = str(sequence)
        
        # 格式化序列显示，去除多余的空格和括号
        sequence_str = sequence_str.replace(" ", "").replace("[", "").replace("]", "")
        
        # 添加"最优FS序列为："前缀，增加行距
        compact_result += f"最优FS序列为：{sequence_str}\n\n"
            
        # 添加评分信息，保留4位小数
        compact_result += f"序列评分:{score:.4f}"
                
        # 同时将结果显示在右侧结果区域（新增）
        if hasattr(self, 'result_browser'):
            self.result_browser.setText(compact_result)

        # 更新邻桶效应图表
        if sequence:
            # 解析序列字符串为列表
            try:
                if isinstance(sequence, str):
                    # 移除方括号并分割
                    sequence_str = sequence.strip('[]')
                    sequence_list = [int(x.strip()) for x in sequence_str.split(',') if x.strip()]
                else:
                    sequence_list = sequence

                # 计算导弹总数
                missile_count = len(sequence_list) + len(excluded_missiles) if excluded_missiles else len(sequence_list)

                # 更新图表
                self.update_neighbor_effect_chart(sequence_list, missile_count)
            except Exception as e:
                print(f"更新邻桶效应图表时出错: {e}")

        # 同时将去选DD信息显示在右侧结果区域（新增），优化格式
        if hasattr(self, 'excluded_browser'):
            if excluded_missiles:
                # 格式化去选DD显示，去除多余的空格和括号
                excluded_str = str(excluded_missiles).replace(" ", "").replace("[", "").replace("]", "")
                self.excluded_browser.setText(f"去选DD:{excluded_str}")
            else:
                self.excluded_browser.setText("无去选DD")

    def save_to_excel(self, data):
        """保存数据到Excel"""
        import pandas as pd
        df = pd.DataFrame([data])
        df.to_excel("Tdistance.xlsx", index=False, engine='openpyxl')

    def validate_input(self, text):
        """输入验证和更新逻辑"""

        if not text:
            self.valid_num = 0
            global_input_double = 0  # 清空全局变量
            self.Circle.update()
            # 如果有首选导弹，可能需要清除，因为导弹数量变了
            if self.first_missile_id is not None:
                self.first_missile_id = None
            return

        allowed_values = [2, 4, 6, 8, 16]

        # 特别处理输入"1"作为"16"的前缀
        if text == "1":
            self.valid_num = 0
            self.Circle.update()
            # 如果有首选导弹，可能需要清除，因为导弹数量变了
            if self.first_missile_id is not None:
                self.first_missile_id = None
            return

        try:
            num = int(text)
            if num not in allowed_values:
                raise ValueError
        except ValueError:
            self.show_warning("请输入以下数值之一：2、4、6、8、16")
            self.Sum.clear()
            self.valid_num = 0
            self.Circle.update()
            # 如果有首选导弹，可能需要清除，因为导弹数量变了
            if self.first_missile_id is not None:
                self.first_missile_id = None
            return

        # 设置为输入值的两倍
        self.valid_num = num * 2
        self.global_input_double = num * 2
        self.Circle.update()
        
        # 如果有首选导弹且超出了新的导弹数量范围，需要清除
        if self.first_missile_id is not None and self.first_missile_id > num * 2:
            self.first_missile_id = None
            self.show_warning(f"首选导弹编号超出范围，已清除设置")

    def show_warning(self, message):
        """显示警告弹窗"""
        msg = QMessageBox(self)
        msg.setIcon(QMessageBox.Warning)
        msg.setWindowTitle("警告")
        msg.setText(message)
        msg.setStandardButtons(QMessageBox.Ok)
        msg.button(QMessageBox.Ok).setText("确定")
        
        # 设置消息框样式
        msg.setStyleSheet("""
            QMessageBox {
                background-color: #002244;
                color: white;
            }
            QLabel {
                color: white;
            }
            QPushButton {
                background-color: #005588;
                color: white;
                min-width: 80px;
                min-height: 24px;
            }
        """)
        
        msg.exec_()

    def custom_paint_event(self, event):
        """自定义绘制函数（恢复两行布局）"""
        if self.valid_num <= 0:
            return

        painter = QPainter(self.Circle)
        painter.setRenderHint(QPainter.Antialiasing)

        # 设置白色画笔
        pen = QPen(Qt.white)
        pen.setWidth(2)  # 设置边框宽度
        painter.setPen(pen)

        # 计算布局参数（恢复原始两行布局）
        n = self.valid_num
        row1 = n // 2  # 第一行数量
        row2 = n - row1  # 第二行数量

        width = self.Circle.width()
        height = self.Circle.height()
        spacing = 5  # 间距

        # 计算最大列数
        max_cols = max(row1, row2)
        if max_cols == 0:
            return

        # 计算直径（保持两行布局）
        diameter_w = (width - (max_cols - 1) * spacing) / max_cols
        diameter_h = (height - spacing) / 2
        diameter = min(diameter_w, diameter_h)

        if diameter <= 0:
            return

        # 计算垂直位置（居中）
        total_vertical = 2 * diameter + spacing
        start_y = (height - total_vertical) // 2

        # 绘制两行
        self.draw_circle_row(painter, row1, diameter, spacing, start_y, width)
        self.draw_circle_row(painter, row2, diameter, spacing,
                             start_y + diameter + spacing, width)

    def draw_circle_row(self, painter, count, diameter, spacing, y, total_width):
        """绘制单行圆形（保持水平居中）"""
        if count == 0:
            return

        # 计算行宽和起始位置
        row_width = count * diameter + (count - 1) * spacing
        start_x = (total_width - row_width) / 2

        # 绘制圆形
        for i in range(count):
            x = start_x + i * (diameter + spacing)
            painter.drawEllipse(int(x), int(y), int(diameter), int(diameter))


    def update_groupbox_statePower(self, state):
        enable = state == Qt.Checked
        # 遍历QGroupBox内的所有子组件并设置可用状态
        for child in self.PowerBox.findChildren(QWidget):
            child.setEnabled(enable)
        # 可选：保持QGroupBox本身可用，仅控制子组件
        self.PowerBox.setEnabled(True)

    def update_groupbox_stateNT(self, state):
        enable = state == Qt.Checked
        # 遍历QGroupBox内的所有子组件并设置可用状态
        for child in self.NTBox.findChildren(QWidget):
            child.setEnabled(enable)
        # 可选：保持QGroupBox本身可用，仅控制子组件
        self.NTBox.setEnabled(True)

    def update_groupbox_stateAir(self, state):
        enable = state == Qt.Checked
        # 遍历QGroupBox内的所有子组件并设置可用状态
        for child in self.AirBox.findChildren(QWidget):
            child.setEnabled(enable)
        # 可选：保持QGroupBox本身可用，仅控制子组件
        self.AirBox.setEnabled(True)

    # def update_groupbox_stateWater(self, state):
    #     enable = state == Qt.Checked
    #     # 遍历QGroupBox内的所有子组件并设置可用状态
    #     for child in self.WaterBox.findChildren(QWidget):
    #         child.setEnabled(enable)
    #     # 可选：保持QGroupBox本身可用，仅控制子组件
    #     self.WaterBox.setEnabled(True)

    # def update_groupbox_stateOP(self, state):
    #     enable = state == Qt.Checked
    #     # 遍历QGroupBox内的所有子组件并设置可用状态
    #     for child in self.OPWaterBox.findChildren(QWidget):
    #         child.setEnabled(enable)
    #     # 可选：保持QGroupBox本身可用，仅控制子组件
    #     self.OPWaterBox.setEnabled(True)

    # def update_groupbox_stateDD(self, state):
    #     enable = state == Qt.Checked
    #     # 遍历QGroupBox内的所有子组件并设置可用状态
    #     for child in self.DDQuality.findChildren(QWidget):
    #         child.setEnabled(enable)
    #     # 可选：保持QGroupBox本身可用，仅控制子组件
    #     self.DDQuality.setEnabled(True)

    def update_groupbox_stateTube(self, state):
        enable = state == Qt.Checked
        # 遍历QGroupBox内的所有子组件并设置可用状态
        for child in self.TubeQuality.findChildren(QWidget):
            child.setEnabled(enable)
        # 可选：保持QGroupBox本身可用，仅控制子组件
        self.TubeQuality.setEnabled(True)

    def update_groupbox_stateEle(self, state):
        enable = state == Qt.Checked
        # 遍历QGroupBox内的所有子组件并设置可用状态
        for child in self.Eletric.findChildren(QWidget):
            child.setEnabled(enable)
        # 可选：保持QGroupBox本身可用，仅控制子组件
        self.Eletric.setEnabled(True)

    def lock_and_check(self):
        # 锁定所有组件
        self.set_widgets_enabled(self.Setparameter, False)
        # 检查所有 QLineEdit 是否为空
        if self.ControlEle.isChecked():
            line_edits = self.Eletric.findChildren(QLineEdit)
            for line_edit in line_edits:
                if line_edit.text().strip() == "":
                    # 弹出提示窗口
                    msg_box = QMessageBox()
                    msg_box.setWindowTitle("提示")
                    msg_box.setText("电路存在未填写的输入框，请填写。")
                    msg_box.setStandardButtons(QMessageBox.Ok)
                    msg_box.button(QMessageBox.Ok).setText("确定")
                    
                    # 设置消息框样式
                    msg_box.setStyleSheet("""
                        QMessageBox {
                            background-color: #002244;
                            color: white;
                        }
                        QLabel {
                            color: white;
                        }
                        QPushButton {
                            background-color: #005588;
                            color: white;
                            min-width: 80px;
                            min-height: 24px;
                        }
                    """)
                    
                    result = msg_box.exec_()
                    if result == QMessageBox.Ok:
                        # 解锁所有组件
                        self.set_widgets_enabled(self.Setparameter, True)
                    return
        # if self.ControlDD.isChecked():
        #     line_edits = self.DDQuality.findChildren(QLineEdit)
        #     for line_edit in line_edits:
        #         if line_edit.text().strip() == "":
        #             # 弹出提示窗口
        #             msg_box = QMessageBox()
        #             msg_box.setWindowTitle("提示")
        #             msg_box.setText("DD质量分存在未填写的输入框，请填写。")
        #             msg_box.setStandardButtons(QMessageBox.Ok)
        #             result = msg_box.exec_()
        #             if result == QMessageBox.Ok:
        #                 # 解锁所有组件
        #                 self.set_widgets_enabled(self.Setparameter, True)
        #             return
        if self.ControlTube.isChecked():
            line_edits = self.TubeQuality.findChildren(QLineEdit)
            for line_edit in line_edits:
                if line_edit.text().strip() == "":
                    # 弹出提示窗口
                    msg_box = QMessageBox()
                    msg_box.setWindowTitle("提示")
                    msg_box.setText("管路存在未填写的输入框，请填写。")
                    msg_box.setStandardButtons(QMessageBox.Ok)
                    msg_box.button(QMessageBox.Ok).setText("确定")
                    
                    # 设置消息框样式
                    msg_box.setStyleSheet("""
                        QMessageBox {
                            background-color: #002244;
                            color: white;
                        }
                        QLabel {
                            color: white;
                        }
                        QPushButton {
                            background-color: #005588;
                            color: white;
                            min-width: 80px;
                            min-height: 24px;
                        }
                    """)
                    
                    result = msg_box.exec_()
                    if result == QMessageBox.Ok:
                        # 解锁所有组件
                        self.set_widgets_enabled(self.Setparameter, True)
                    return
        # if self.ControlOP.isChecked():
        #     line_edits = self.OPWaterBox.findChildren(QLineEdit)
        #     for line_edit in line_edits:
        #         if line_edit.text().strip() == "":
        #             # 弹出提示窗口
        #             msg_box = QMessageBox()
        #             msg_box.setWindowTitle("提示")
        #             msg_box.setText("排/补水存在未填写的输入框，请填写。")
        #             msg_box.setStandardButtons(QMessageBox.Ok)
        #             result = msg_box.exec_()
        #             if result == QMessageBox.Ok:
        #                 # 解锁所有组件
        #                 self.set_widgets_enabled(self.Setparameter, True)
        #             return
        # if self.ControlWater.isChecked():
        #     line_edits = self.WaterBox.findChildren(QLineEdit)
        #     for line_edit in line_edits:
        #         if line_edit.text().strip() == "":
        #             # 弹出提示窗口
        #             msg_box = QMessageBox()
        #             msg_box.setWindowTitle("提示")
        #             msg_box.setText("水源存在未填写的输入框，请填写。")
        #             msg_box.setStandardButtons(QMessageBox.Ok)
        #             result = msg_box.exec_()
        #             if result == QMessageBox.Ok:
        #                 # 解锁所有组件
        #                 self.set_widgets_enabled(self.Setparameter, True)
        #             return
        if self.ControlAir.isChecked():
            line_edits = self.AirBox.findChildren(QLineEdit)
            for line_edit in line_edits:
                if line_edit.text().strip() == "":
                    # 弹出提示窗口
                    msg_box = QMessageBox()
                    msg_box.setWindowTitle("提示")
                    msg_box.setText("气源存在未填写的输入框，请填写。")
                    msg_box.setStandardButtons(QMessageBox.Ok)
                    msg_box.button(QMessageBox.Ok).setText("确定")
                    
                    # 设置消息框样式
                    msg_box.setStyleSheet("""
                        QMessageBox {
                            background-color: #002244;
                            color: white;
                        }
                        QLabel {
                            color: white;
                        }
                        QPushButton {
                            background-color: #005588;
                            color: white;
                            min-width: 80px;
                            min-height: 24px;
                        }
                    """)
                    
                    result = msg_box.exec_()
                    if result == QMessageBox.Ok:
                        # 解锁所有组件
                        self.set_widgets_enabled(self.Setparameter, True)
                    return
        if self.ControlNT.isChecked():
            line_edits = self.NTBox.findChildren(QLineEdit)
            for line_edit in line_edits:
                if line_edit.text().strip() == "":
                    # 弹出提示窗口
                    msg_box = QMessageBox()
                    msg_box.setWindowTitle("提示")
                    msg_box.setText("邻T存在未填写的输入框，请填写。")
                    msg_box.setStandardButtons(QMessageBox.Ok)
                    msg_box.button(QMessageBox.Ok).setText("确定")
                    
                    # 设置消息框样式
                    msg_box.setStyleSheet("""
                        QMessageBox {
                            background-color: #002244;
                            color: white;
                        }
                        QLabel {
                            color: white;
                        }
                        QPushButton {
                            background-color: #005588;
                            color: white;
                            min-width: 80px;
                            min-height: 24px;
                        }
                    """)
                    
                    result = msg_box.exec_()
                    if result == QMessageBox.Ok:
                        # 解锁所有组件
                        self.set_widgets_enabled(self.Setparameter, True)
                    return
        # if self.ControlP.isChecked():
        #     if self.Acontrol.isChecked():
        #         if self.A.text().strip() == "":
        #             # 弹出提示窗口
        #             msg_box = QMessageBox()
        #             msg_box.setWindowTitle("提示")
        #             msg_box.setText("强约束A存在未填写的输入框，请填写。")
        #             msg_box.setStandardButtons(QMessageBox.Ok)
        #             result = msg_box.exec_()
        #             if result == QMessageBox.Ok:
        #                 # 解锁所有组件
        #                 self.set_widgets_enabled(self.Setparameter, True)
        #             return
        #     if self.Bcontrol.isChecked():
        #         if self.B.text().strip() == "":
        #             # 弹出提示窗口
        #             msg_box = QMessageBox()
        #             msg_box.setWindowTitle("提示")
        #             msg_box.setText("强约束B存在未填写的输入框，请填写。")
        #             msg_box.setStandardButtons(QMessageBox.Ok)
        #             result = msg_box.exec_()
        #             if result == QMessageBox.Ok:
        #                 # 解锁所有组件
        #                 self.set_widgets_enabled(self.Setparameter, True)
        #             return
        #     if self.Bcontrol.isChecked():
        #         if self.B.text().strip() == "":
        #             # 弹出提示窗口
        #             msg_box = QMessageBox()
        #             msg_box.setWindowTitle("提示")
        #             msg_box.setText("强约束B存在未填写的输入框，请填写。")
        #             msg_box.setStandardButtons(QMessageBox.Ok)
        #             result = msg_box.exec_()
        #             if result == QMessageBox.Ok:
        #                 # 解锁所有组件
        #                 self.set_widgets_enabled(self.Setparameter, True)
        #             return
        #     if self.Ccontrol.isChecked():
        #         if self.C.text().strip() == "":
        #             # 弹出提示窗口
        #             msg_box = QMessageBox()
        #             msg_box.setWindowTitle("提示")
        #             msg_box.setText("强约束C存在未填写的输入框，请填写。")
        #             msg_box.setStandardButtons(QMessageBox.Ok)
        #             result = msg_box.exec_()
        #             if result == QMessageBox.Ok:
        #                 # 解锁所有组件
        #                 self.set_widgets_enabled(self.Setparameter, True)
        #             return
        #     if self.Dcontrol.isChecked():
        #         if self.D.text().strip() == "":
        #             # 弹出提示窗口
        #             msg_box = QMessageBox()
        #             msg_box.setWindowTitle("提示")
        #             msg_box.setText("强约束D存在未填写的输入框，请填写。")
        #             msg_box.setStandardButtons(QMessageBox.Ok)
        #             result = msg_box.exec_()
        #             if result == QMessageBox.Ok:
        #                 # 解锁所有组件
        #                 self.set_widgets_enabled(self.Setparameter, True)
        #             return
        #     if self.Econtrol.isChecked():
        #         if self.E.text().strip() == "":
        #             # 弹出提示窗口
        #             msg_box = QMessageBox()
        #             msg_box.setWindowTitle("提示")
        #             msg_box.setText("强约束E存在未填写的输入框，请填写。")
        #             msg_box.setStandardButtons(QMessageBox.Ok)
        #             result = msg_box.exec_()
        #             if result == QMessageBox.Ok:
        #                 # 解锁所有组件
        #                 self.set_widgets_enabled(self.Setparameter, True)
        #             return
        #     if self.Fcontrol.isChecked():
        #         if self.F.text().strip() == "":
        #             # 弹出提示窗口
        #             msg_box = QMessageBox()
        #             msg_box.setWindowTitle("提示")
        #             msg_box.setText("强约束F存在未填写的输入框，请填写。")
        #             msg_box.setStandardButtons(QMessageBox.Ok)
        #             result = msg_box.exec_()
        #             if result == QMessageBox.Ok:
        #                 # 解锁所有组件
        #                 self.set_widgets_enabled(self.Setparameter, True)
        #             return
        #     if self.Gcontrol.isChecked():
        #         if self.G.text().strip() == "":
        #             # 弹出提示窗口
        #             msg_box = QMessageBox()
        #             msg_box.setWindowTitle("提示")
        #             msg_box.setText("强约束G存在未填写的输入框，请填写。")
        #             msg_box.setStandardButtons(QMessageBox.Ok)
        #             result = msg_box.exec_()
        #             if result == QMessageBox.Ok:
        #                 # 解锁所有组件
        #                 self.set_widgets_enabled(self.Setparameter, True)
        #             return

        # 以上是所有输入验证逻辑
        # 在这里添加调用DQN算法的代码
        try:
            # 导入DQN算法函数
            import sys
            import os
            
            # 获取missile_launch_rl.py所在路径
            # 注意: 这里需要根据实际情况修改路径
            missile_rl_path = os.path.dirname(os.path.abspath(__file__))  # 当前文件夹
            missile_rl_path = os.path.join(os.path.dirname(missile_rl_path), "missile_launch_rl.py")
            
            if not os.path.exists(missile_rl_path):
                # 如果找不到默认路径，尝试其他可能的路径
                possible_paths = [
                    r"C:\Users\<USER>\Desktop\武汉719\missile_launch_rl.py",  # 尝试父目录
                    r"missile_launch_rl.py"  # 尝试相对路径
                ]
                
                for path in possible_paths:
                    if os.path.exists(path):
                        missile_rl_path = path
                        break
            
            # 如果文件不存在，显示错误并返回
            if not os.path.exists(missile_rl_path):
                self.show_error(f"找不到DQN算法文件，请确保missile_launch_rl.py在正确的位置")
                return
                
            # 导入DQN算法
            sys.path.append(os.path.dirname(missile_rl_path))
            
            try:
                from missile_launch_rl import get_optimal_sequence
                
                # 获取T距文件路径
                tdistance_path = os.path.join(os.path.dirname(os.path.abspath(__file__)), "Tdistance.xlsx")
                
                # 检查文件是否存在
                if not os.path.exists(tdistance_path):
                    self.show_error(f"找不到T距文件: {tdistance_path}")
                    return
                
                # 获取列数（从界面读取）
                columns_count = 0
                if hasattr(self, 'Sum') and self.Sum.text():
                    columns_count = int(self.Sum.text())
                else:
                    # 如果界面上没有设置，使用默认值2
                    columns_count = 2
                    
                if columns_count <= 0 or columns_count > 16:
                    self.show_warning("列数必须在1到16之间，使用默认值2")
                    columns_count = 2
                
                # 获取首选导弹编号
                first_missile_id = None
                if hasattr(self, 'first_missile_id') and self.first_missile_id is not None:
                    first_missile_id = self.first_missile_id
                elif hasattr(self, 'firstMissileComboBox') and self.firstMissileComboBox.currentText() != "无":
                    try:
                        first_missile_id = int(self.firstMissileComboBox.currentText())
                        max_missile_id = columns_count * 2  # 导弹数量 = 列数 * 2
                        if first_missile_id < 1 or first_missile_id > max_missile_id:
                            self.show_warning(f"首选导弹编号必须在1-{max_missile_id}之间，已忽略")
                            first_missile_id = None
                        self.first_missile_id = None
                    except ValueError:
                        self.show_warning("首选导弹编号无效，已忽略")
                        first_missile_id = None
                        self.first_missile_id = None
                
                # 获取被去选的导弹列表
                excluded_missiles = []
                if hasattr(self, 'dd_manager') and self.dd_manager:
                    excluded_missiles = self.dd_manager.getSelectedDDs()

                # 获取资源状态
                resource_states = {}
                if hasattr(self, 'resource_data_manager') and self.resource_data_manager:
                    resource_states = self.resource_data_manager.resources

                # 获取共打发射数量
                launch_count = self.launch_count
                
                # 显示计算中的提示
                first_missile_text = f"，首选DD: {first_missile_id}" if first_missile_id else ""
                excluded_text = f"，去选DD: {excluded_missiles}" if excluded_missiles else ""
                launch_text = f"，共打: {launch_count}发" if launch_count > 0 else ""
                self.textBrowser.setText(f"正在计算最优FS序列，请稍候...{first_missile_text}{excluded_text}{launch_text}")
                self.textBrowser.repaint()  # 强制更新UI
                
                # 调用DQN算法计算最优发射序列，传递首选导弹编号
                # 设置verbose=False以隐藏控制台输出，包括加载导弹距离的详细信息
                if first_missile_id is None:
                    self.show_warning("请先设置首选DD编号")
                    self.textBrowser.setText("请先设置首选DD编号")
                    return
                    
                sequence, score = get_optimal_sequence(
                    tdistance_path,
                    columns_count,
                    first_missile_id,
                    verbose=False,
                    excluded_missiles=excluded_missiles,
                    resource_states=resource_states
                )
                
                # 如果设置了launch_count，则限制序列长度
                if launch_count > 0 and launch_count < len(sequence):
                    sequence = sequence[:launch_count]
                    # 重新计算得分
                    score = score * (launch_count / len(sequence))
                
                # 显示结果
                result_text = f"最优FS序列计算完成！\n\n"
                result_text += f"DD数量: {columns_count * 2}\n"
                if first_missile_id:
                    result_text += f"首选DD: {first_missile_id}\n"
                
                # 添加共打发射数量信息
                if launch_count > 0:
                    result_text += f"共打: {launch_count}发\n"
                
                # 添加去选DD信息
                if excluded_missiles:
                    result_text += f"DD去选: {excluded_missiles}\n"
                    
                result_text += f"最优FS序列: {sequence}\n"
                result_text += f"序列评分: {score:.4f}\n"
                
                # 将结果显示在界面上
                if hasattr(self, 'textBrowser'):
                    self.textBrowser.setText(result_text)
                else:
                    print(result_text)  # 如果没有文本浏览器，则打印到控制台
                
                # 创建结果显示对话框
                from PyQt5.QtWidgets import QDialog, QVBoxLayout, QLabel, QPushButton, QTextEdit
                
                dialog = QDialog(self)
                dialog.setWindowTitle("计算结果")
                dialog.setMinimumWidth(400)
                dialog.setMinimumHeight(300)
                
                layout = QVBoxLayout()
                
                # 添加标题
                title_label = QLabel("DD FS顺序优化结果")
                title_label.setStyleSheet("font-size: 16pt; font-weight: bold; color: #00FFFF;")
                layout.addWidget(title_label)
                
                # 添加结果文本框
                result_textedit = QTextEdit()
                result_textedit.setReadOnly(True)
                result_textedit.setStyleSheet("background-color: #001428; color: white; font-size: 12pt;")
                result_textedit.setText(result_text)
                layout.addWidget(result_textedit)
                
                # 添加确定按钮
                ok_button = QPushButton("确定")
                ok_button.clicked.connect(dialog.accept)
                ok_button.setStyleSheet("background-color: #005588; color: white;")
                layout.addWidget(ok_button)
                
                dialog.setLayout(layout)
                dialog.setStyleSheet("background-color: #002244;")
                
                # 显示对话框
                dialog.exec_()
                    
            except ImportError as e:
                self.show_error(f"导入DQN算法失败: {e}")
            except Exception as e:
                self.show_error(f"计算最优FS序列时出错: {e}")
                import traceback
                traceback.print_exc()
                
        except Exception as e:
            self.show_error(f"调用DQN算法失败: {e}")
            import traceback
            traceback.print_exc()

    def set_groupbox_enabled(self, parent_groupbox, enabled):
        """递归设置父组件及其子组件的可用状态"""
        for child in parent_groupbox.children():
            if isinstance(child, QGroupBox):
                    child.setEnabled(enabled)
                    self.set_groupbox_enabled(child, enabled)

    def set_widgets_enabled(self, parent_widget, enabled):
        """递归设置父组件及其子组件的可用状态"""
        for child in parent_widget.children():
            if isinstance(child, QWidget):
                if child != self.Operate and child != self.Certain:
                    child.setEnabled(enabled)
                    self.set_widgets_enabled(child, enabled)

    def set_checkbox_enabled(self, parent_widget, enabled):
        self.ControlAir.setEnabled(enabled)
        #self.ControlWater.setEnabled(enabled)
        #self.ControlOP.setEnabled(enabled)
        #self.ControlDD.setEnabled(enabled)
        self.ControlTube.setEnabled(enabled)
        self.ControlEle.setEnabled(enabled)
        self.Init.setEnabled(enabled)
        self.ControlP.setEnabled(enabled)
        self.ControlNT.setEnabled(enabled)

    def unlock_widgets(self):
        # 点击按钮后，将 QWidget 内的所有组件设置为可用
        self.set_checkbox_enabled(self.Setparameter, True)

    def init_display(self):
        """初始化按钮点击时的处理函数，初始化已激活的各个部分"""
        try:
            # 调整textBrowser的大小，使其能够显示更多内容
            if hasattr(self, 'textBrowser'):
                self.textBrowser.setMinimumHeight(120)  # 增加最小高度
                self.textBrowser.setMaximumHeight(200)  # 增加最大高度
            
            # 首先初始化设计栏 - 列数默认为8
            try:
                # 设置列数为8 - 找到正确的输入框并设置值
                if hasattr(self, 'Sum'):
                    # 设置列数为8
                    self.Sum.setText("8")
                    # 手动触发输入验证以更新全局变量
                    self.validate_input("8")
                    # 确保全局变量和显示更新
                    self.valid_num = 16  # 8列 * 2 = 16个导弹
                    self.global_input_double = 16
                    # 更新圆形显示
                    if hasattr(self, 'Circle'):
                        self.Circle.update()
                
                # 初始化T距设置 - 检查文件是否存在
                try:
                    # 检查Tdistance.xlsx是否存在
                    tdistance_path = os.path.join(os.path.dirname(os.path.abspath(__file__)), "Tdistance.xlsx")
                    if not os.path.exists(tdistance_path):
                        # 如果不存在，创建一个基本的默认T距数据
                        # 注意：这里只是创建一个基本结构，实际值应该由用户设置
                        default_t_data = {}
                        for i in range(1, 8):  # r1到r7
                            default_t_data[f"r{i}"] = 1.0
                        default_t_data["l"] = 2.0
                        
                        # 保存基本的默认T距数据到Excel
                        self.save_to_excel(default_t_data)
                    
                    # 在T距设置完成后立即在后台训练模型
                    self.train_dqn_model()
                except Exception as e:
                    print(f"初始化T距设置时出错: {str(e)}")
            except Exception as e:
                print(f"设计栏初始化失败: {str(e)}")
            
            # 2. DD健康分初始化 - 所有导弹健康分默认为100
            try:
                # 获取当前列数对应的导弹数量
                columns_count = 8  # 默认值
                if hasattr(self, 'Sum') and self.Sum.text():
                    try:
                        columns_count = int(self.Sum.text())
                    except ValueError:
                        columns_count = 8
                
                missile_count = columns_count * 2
                
                # 创建所有导弹健康分为100的字典
                scores = {}
                for i in range(1, missile_count + 1):
                    scores[i] = 100
                
                # 设置健康分
                if hasattr(self, 'dd_manager') and self.dd_manager:
                    # 设置健康分
                    self.dd_manager.setScores(scores)
                    
                    # 保存到Excel
                    self.dd_manager.saveToExcel()
                    
                    # 清除去选的DD
                    self.dd_manager.setSelectedDDs([])
            except Exception as e:
                print(f"DD健康分初始化失败: {str(e)}")
            
            # 3. 船舶健康分初始化 - 所有健康分默认为100
            try:
                # 创建所有船舶健康分为100的字典
                scores = {}
                for i in range(1, 17):  # 假设最多16个船舶
                    scores[i] = {
                        'pipeline': 100,  # 管路健康分
                        'circuit': 100    # 电路健康分
                    }
                
                # 设置健康分
                if hasattr(self, 'ship_data_manager') and self.ship_data_manager:
                    # 设置健康分
                    self.ship_data_manager.setScores(scores)
                    
                    # 保存到Excel
                    self.ship_data_manager.saveScoresToExcel()
            except Exception as e:
                print(f"船舶健康分初始化失败: {str(e)}")
            
            # 4. 气源/水源/液压源初始化 - 所有状态默认为OK
            try:
                # 创建所有资源状态为OK的字典
                resources = {}
                for i in range(1, missile_count + 1):  # 根据实际导弹数量设置
                    resources[i] = {
                        'gas': 'OK',
                        'water': 'OK',
                        'hydraulic': 'OK'
                    }
                
                # 设置资源
                if hasattr(self, 'resource_data_manager') and self.resource_data_manager:
                    # 设置资源
                    self.resource_data_manager.setResources(resources)
                    
                    # 保存到Excel
                    self.resource_data_manager.saveToExcel()
            except Exception as e:
                print(f"气源/水源/液压源初始化失败: {str(e)}")
            
            # 清除共打数量
            try:
                self.launch_count = 0
                if hasattr(self, 'Fs_sum'):
                    self.Fs_sum.clear()
            except Exception as e:
                print(f"清除共打数量失败: {str(e)}")
                
            # 清除首选导弹
            try:
                self.first_missile_id = None
                if hasattr(self, 'update_first_missile_button'):
                    self.update_first_missile_button()
            except Exception as e:
                print(f"清除首选导弹失败: {str(e)}")
            
            # 清空结果显示
            try:
                if hasattr(self, 'textBrowser'):
                    self.textBrowser.clear()
                if hasattr(self, 'textBrowser_2'):
                    self.textBrowser_2.clear()
                # 清空右侧结果区域
                if hasattr(self, 'result_browser'):
                    self.result_browser.clear()
                if hasattr(self, 'excluded_browser'):
                    self.excluded_browser.clear()
                    
                # 确保结果区域的大小和位置正确
                if hasattr(self, 'groupBox'):
                    # 调整groupBox的位置和大小，与新的大界面布局保持一致
                    self.groupBox.setGeometry(650, 20, 350, 400)
            except Exception as e:
                print(f"清空结果显示失败: {str(e)}")
            
            # 只显示一个初始化完成的提示窗口
            self.show_info("初始化完成！")
            
        except Exception as e:
            self.show_error(f"初始化过程中出错: {str(e)}")
            import traceback
            traceback.print_exc()

    def update_time(self):
        # 获取当前时间
        current_time = time.strftime("%Y-%m-%d %H:%M:%S", time.localtime())
        # 拼接"当前时间"文本和时间
        display_text = f"时间: {current_time} 版本号：v1.0"
        # 更新时间标签的文本
        self.timeLabel.setText(display_text)

    def initUI(self):
        # 连接按钮信号
        self.pushButton.clicked.connect(lambda: self.stackedWidget.setCurrentIndex(0))
        self.pushButton_2.clicked.connect(lambda: self.stackedWidget.setCurrentIndex(1))
        self.pushButton_3.clicked.connect(lambda: self.stackedWidget.setCurrentIndex(2))
        self.pushButton_4.clicked.connect(lambda: self.stackedWidget.setCurrentIndex(3))
        self.pushButton_7.clicked.connect(lambda: self.stackedWidget.setCurrentIndex(4))
        self.pushButton_8.clicked.connect(lambda: self.stackedWidget.setCurrentIndex(5))
        self.pushButton_9.clicked.connect(lambda: self.stackedWidget.setCurrentIndex(6))

        # 设置按钮可选中
        self.pushButton.setCheckable(True)
        self.pushButton_2.setCheckable(True)
        self.pushButton_3.setCheckable(True)
        self.pushButton_4.setCheckable(True)
        self.pushButton_7.setCheckable(True)
        self.pushButton_8.setCheckable(True)
        self.pushButton_9.setCheckable(True)

        # 设置按钮组
        self.buttonGroup = QButtonGroup()
        self.buttonGroup.setExclusive(True)  # 设置互斥
        self.buttonGroup.addButton(self.pushButton)
        self.buttonGroup.addButton(self.pushButton_2)
        self.buttonGroup.addButton(self.pushButton_3)
        self.buttonGroup.addButton(self.pushButton_4)
        self.buttonGroup.addButton(self.pushButton_7)
        self.buttonGroup.addButton(self.pushButton_8)
        self.buttonGroup.addButton(self.pushButton_9)

        # 设置初始状态
        self.pushButton.setChecked(True)  # 默认选中第一个按钮
        self.stackedWidget.setCurrentIndex(0)  # 设置 stackedWidget 初始页为第一页

        # pushButton_5按钮：加载数据集
        self.pushButton_5.clicked.connect(self.load_dataset)

        # pushButton_6按钮：生成数据集
        self.pushButton_6.clicked.connect(self.generate_dataset)

        # pushButton_10按钮：获取 group_id
        self.pushButton_10.clicked.connect(self.algorithm)

        # pushButton_11按钮：输出结果
        self.pushButton_11.clicked.connect(self.read_orders_csv)

    # 鼠标移动窗口函数
    def centralWidget_mousePressEvent(self, event):
        """标题栏的鼠标按下事件"""
        if event.button() == QtCore.Qt.LeftButton:
            self._startPos = event.globalPos()
            self._windowPos = self.frameGeometry().topLeft()

    def centralWidget_mouseMoveEvent(self, event):
        """标题栏的鼠标移动事件"""
        if event.buttons() == QtCore.Qt.LeftButton:
            relativePos = event.globalPos() - self._startPos
            self.move(self._windowPos + relativePos)

    # 加载数据集
    def load_dataset(self):
        # 打开文件对话框选择 CSV 文件
        options = QFileDialog.Options()
        file_name, _ = QFileDialog.getOpenFileName(self, "Open CSV File", "", "CSV Files (*.csv);;All Files (*)",
                                                   options=options)
        if not file_name:
            return

        # 读取 CSV 文件并加载到表格
        with open(file_name, "r", encoding="utf-8") as file:
            reader = csv.reader(file)
            data = list(reader)

            # 设置表格行数和列数
            self.tableWidget.setRowCount(len(data))
            self.tableWidget.setColumnCount(len(data[0]))

            # 填充表格数据
            for row_idx, row in enumerate(data):
                for col_idx, value in enumerate(row):
                    self.tableWidget.setItem(row_idx, col_idx, QTableWidgetItem(value))

    # 生成数据集
    def generate_dataset(self):
        # 生成数据集
        Dataset.mydataset()

        # 显示成功提示
        self.show_success_message()

    # 显示成功提示
    def show_success_message(self):
        """
        显示提示框,提示"生成数据集成功!已保存为'dateset.csv'"
        """
        msg = QMessageBox()
        msg.setStyleSheet("")  # 清空样式
        msg.setIcon(QMessageBox.Information)  # 设置图标为信息提示
        msg.setWindowTitle("成功")  # 对话框标题
        msg.setText("生成数据集成功!已保存为\"dataset.csv\"")  # 提示文字
        msg.setStandardButtons(QMessageBox.Ok)  # 设置按钮
        msg.exec_()  # 显示对话框

    # 通用的 QMessageBox 函数
    def show_message_box(self, title, message, icon=QMessageBox.Information, button_text="确认"):
        """显示消息框"""
        msg_box = QMessageBox(self)
        msg_box.setIcon(icon)
        msg_box.setWindowTitle(title)
        msg_box.setText(message)
        msg_box.setStandardButtons(QMessageBox.Ok)
        
        # 设置按钮文本
        ok_button = msg_box.button(QMessageBox.Ok)
        ok_button.setText(button_text)

        # 设置消息框样式
        msg_box.setStyleSheet("""
            QMessageBox {
                background-color: #002244;
                color: white;
            }
            QLabel {
                color: white;
            }
            QPushButton {
                background-color: #005588;
                color: white;
                min-width: 80px;
                min-height: 24px;
            }
        """)
        
        return msg_box.exec_()

    # 获取 group_id
    def get_group_id(self):
        """
        从 QTextEdit 获取 group_id
        """
        group_id = self.textEdit.toPlainText().strip()  # 读取文本内容并去除两端空格

        # 检查输入是否为数字
        if not group_id.isdigit():
            QMessageBox.warning(self, "group_id错误", "请输入有效的group_id!")
            return None  # 返回 None 表示无效输入

        return int(group_id)  # 返回整数类型的 group_id

    # 算法处理，按钮点击事件
    def algorithm(self):
        group_id = self.get_group_id()  # 调用获取 group_id 的函数
        if group_id is not None:
            # 检查附加条件
            if 0 < group_id <= 100:  # 检查是否在 [0, 100] 的范围内
                # print(f"按钮点击后获取的 group_id 是：{group_id}")
                # 在这里处理符合条件的 group_id
                # QMessageBox.information(self, "成功", f"有效的 group_id 是：{group_id}")
                self.show_message_box("成功", f"group_id = {group_id}，处理成功")
            else:
                # 提示不符合范围的情况
                # QMessageBox.warning(self, "范围错误", "请输入 0 到 100 之间的整数！")
                self.show_message_box("范围错误", "请输入 1 到 100 之间的整数！")

    # 输出结果显示
    def read_orders_csv(self):
        """
        点击按钮后读取 orders.csv,根据 group_id 显示结果
        """
        group_id = self.get_group_id()  # 调用获取 group_id 的函数

        try:
            # 读取 orders.csv 文件
            orders = pd.read_csv("orders.csv")

            # 查找对应的 group_id 数据
            row = orders[orders["group_id"] == group_id]
            if row.empty:
                # QMessageBox.warning(self, "未找到数据", f"未找到 group_id 为 {group_id} 的记录！")
                self.show_message_box("未找到数据", f"未找到 group_id 为 {group_id} 的记录！", QMessageBox.Warning)
                return

            # 提取 firing_order 和 invalid_missiles
            firing_order = row["firing_order"].values[0]
            invalid_missiles = row["invalid"].values[0]

            # 将结果显示到 textBrowser 和 textBrowser_2
            self.textBrowser.setText(f"{firing_order}")
            self.textBrowser_2.setText(f"{invalid_missiles}")

        except FileNotFoundError:
            # QMessageBox.critical(self, "文件未找到", "orders.csv 文件未找到，请检查路径！")
            self.show_message_box("文件未找到", "orders.csv 文件未找到，请检查路径！", QMessageBox.Critical)
        except Exception as e:
            # QMessageBox.critical(self, "错误", f"发生错误：{e}")
            self.show_message_box("错误", f"发生错误：{e}", QMessageBox.Critical)

    def validate_launch_count(self, text):
        try:
            launch_count = int(text)
            if launch_count < 0 or launch_count > 999:
                raise ValueError
        except ValueError:
            self.show_warning("请输入0到999之间的整数")
            self.Fs_sum.clear()
            self.launch_count = 0
            return
        self.launch_count = launch_count

    def create_neighbor_effect_chart(self):
        """创建邻桶效应图表"""
        try:
            # 导入邻桶效应图表组件
            from neighbor_effect_chart import NeighborEffectChart

            # 创建邻桶效应图表
            self.neighbor_chart = NeighborEffectChart(self.Setparameter)

            # 计算图表位置，适应1600x1000大界面布局
            # groupBox在(650,20,350,400)，按钮在(720,290,180,80)
            # 邻桶效应图表放在界面右上角，增大尺寸
            chart_x = 1020  # 给按钮和注水图表留出空间
            chart_y = 20    # 顶部对齐
            chart_width = 560  # 增大宽度
            chart_height = 350  # 增大高度

            # 检查是否超出界面范围
            if chart_x + chart_width > 1600:  # 界面宽度1600
                chart_width = 1600 - chart_x - 20

            self.neighbor_chart.setGeometry(chart_x, chart_y, chart_width, chart_height)

            # 显示图表
            self.neighbor_chart.show()

            # 初始化动态展示相关变量
            self.dynamic_timer = None
            self.current_sequence = []
            self.current_missile_count = 0
            self.dynamic_step = 0

        except ImportError as e:
            print(f"导入邻桶效应图表失败: {e}")
        except Exception as e:
            print(f"创建邻桶效应图表失败: {e}")

    def create_water_balance_chart(self):
        """创建注水/吹除平衡图表"""
        try:
            # 强力清理可能存在的旧图表
            if hasattr(self, 'water_balance_chart'):
                try:
                    # 清理图表内部资源
                    if hasattr(self.water_balance_chart, 'cleanup_chart'):
                        self.water_balance_chart.cleanup_chart()
                    # 隐藏和关闭窗口
                    self.water_balance_chart.hide()
                    self.water_balance_chart.close()
                    self.water_balance_chart.deleteLater()
                    delattr(self, 'water_balance_chart')
                    print("旧的注水图表已清理")
                except Exception as e:
                    print(f"清理旧图表时出错: {e}")
                    pass

            # 导入注水/吹除平衡图表组件
            from water_balance_chart import WaterBalanceChart

            # 创建注水/吹除平衡图表，设置正确的父窗口
            self.water_balance_chart = WaterBalanceChart(self)

            # 计算图表位置，确保与邻桶效应图表完全垂直对齐，避免与其他元素重叠
            # 更新后的布局：
            # - 邻桶效应图表在右上角位置 (1020, 20, 560, 350) - 已增大
            # - DD FS结果区域在 (650, 20, 350, 400)
            # - 按钮区域在 (720, 450, 241, 85) 开始
            # 注水图表应该放在邻桶效应图表正下方，但要在按钮上方
            chart_x = 1150  # 与邻桶效应图表X坐标完全对齐
            chart_y = 490   # 邻桶效应图表下方，留出足够间距 (20+350+20)
            chart_width = 560  # 与邻桶效应图表宽度完全一致
            chart_height = 350  # 与邻桶效应图表高度完全一致

            # 检查是否超出界面范围
            if chart_x + chart_width > 1600:  # 界面宽度1600
                chart_width = 1600 - chart_x - 20
            if chart_y + chart_height > 1000:  # 界面高度1000
                chart_height = 1000 - chart_y - 20

            self.water_balance_chart.setGeometry(chart_x, chart_y, chart_width, chart_height)

            # 显示图表
            self.water_balance_chart.show()

            print(f"注水/吹除平衡图表创建成功，位置: ({chart_x}, {chart_y}, {chart_width}, {chart_height})")

        except ImportError as e:
            print(f"导入注水/吹除平衡图表失败: {e}")
        except Exception as e:
            print(f"创建注水/吹除平衡图表失败: {e}")

    def update_neighbor_effect_chart(self, sequence, missile_count):
        """更新邻桶效应图表和注水/吹除平衡图表 - 启动动态展示"""
        try:
            # 保存序列数据
            self.current_sequence = sequence
            self.current_missile_count = missile_count

            # 更新邻桶效应图表
            if hasattr(self, 'neighbor_chart'):
                # 启动邻桶效应动态展示
                self.start_dynamic_neighbor_effect()

            # 更新注水/吹除平衡图表
            if hasattr(self, 'water_balance_chart'):
                # 启动注水/吹除平衡动态展示
                self.water_balance_chart.update_chart(sequence, missile_count)
                print(f"注水/吹除平衡图表已更新，序列: {sequence}")

        except Exception as e:
            print(f"更新图表失败: {e}")

    def start_dynamic_neighbor_effect(self):
        """启动邻桶效应动态展示"""
        try:
            # 停止之前的动态展示
            if hasattr(self, 'dynamic_timer') and self.dynamic_timer:
                self.dynamic_timer.stop()

            # 创建定时器
            from PyQt5.QtCore import QTimer
            self.dynamic_timer = QTimer()
            self.dynamic_timer.timeout.connect(self.update_dynamic_neighbor_effect)

            # 重置步骤
            self.dynamic_step = 0

            # 开始动态展示，每500毫秒更新一次
            self.dynamic_timer.start(500)

        except Exception as e:
            print(f"启动动态展示失败: {e}")

    def update_dynamic_neighbor_effect(self):
        """更新动态邻桶效应展示"""
        try:
            if not hasattr(self, 'neighbor_chart') or not self.current_sequence:
                return

            # 如果展示完成，停止定时器
            if self.dynamic_step > len(self.current_sequence):
                if hasattr(self, 'dynamic_timer') and self.dynamic_timer:
                    self.dynamic_timer.stop()
                return

            # 获取当前步骤的发射序列
            current_fired = self.current_sequence[:self.dynamic_step]

            # 更新图表
            self.neighbor_chart.update_chart(current_fired, self.current_missile_count)

            # 更新步骤
            self.dynamic_step += 1

        except Exception as e:
            print(f"动态展示更新失败: {e}")

    def stop_dynamic_neighbor_effect(self):
        """停止邻桶效应动态展示"""
        try:
            if hasattr(self, 'dynamic_timer') and self.dynamic_timer:
                self.dynamic_timer.stop()
        except Exception as e:
            print(f"停止动态展示失败: {e}")


if __name__ == "__main__":
    app = QApplication(sys.argv)
    window = MainWindow()
    window.show()
    sys.exit(app.exec_())
