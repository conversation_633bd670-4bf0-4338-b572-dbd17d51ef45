#!/usr/bin/env python3
# -*- coding: utf-8 -*-

"""
修复后的邻桶效应图表演示
展示正确的界面布局和动态效果
"""

import sys
from PyQt5.QtWidgets import QApplication, QMainWindow, QVBoxLayout, QWidget, QPushButton, QHBoxLayout, QLabel, QTextEdit
from PyQt5.QtCore import Qt, QTimer
from neighbor_effect_chart import NeighborEffectChart

class FixedNeighborEffectDemo(QMainWindow):
    def __init__(self):
        super().__init__()
        self.setWindowTitle("修复后的邻桶效应图表演示 - 正确布局和动态展示")
        # 设置窗口大小与主界面一致
        self.setGeometry(100, 100, 1325, 852)
        
        # 设置深色主题
        self.setStyleSheet("""
            QMainWindow {
                background-color: #1e1e1e;
                color: white;
            }
            QPushButton {
                background-color: #004293;
                color: white;
                border: 1px solid #00FFFF;
                border-radius: 5px;
                padding: 8px;
                font-size: 11px;
                margin: 2px;
            }
            QPushButton:hover {
                background-color: #0066cc;
            }
            QLabel {
                color: #00FFFF;
                font-size: 12px;
                padding: 5px;
            }
        """)
        
        # 创建中央部件
        central_widget = QWidget()
        self.setCentralWidget(central_widget)
        
        # 创建主布局
        main_layout = QVBoxLayout(central_widget)
        
        # 添加标题
        title_label = QLabel("邻桶效应图表修复演示 - 正确的界面布局和动态展示")
        title_label.setAlignment(Qt.AlignCenter)
        title_label.setStyleSheet("font-size: 16px; color: #00FFFF; font-weight: bold; padding: 10px;")
        main_layout.addWidget(title_label)
        
        # 创建模拟主界面的布局区域
        interface_widget = QWidget()
        interface_widget.setFixedHeight(400)
        interface_layout = QHBoxLayout(interface_widget)
        interface_layout.setContentsMargins(0, 0, 0, 0)
        
        # 左侧空白区域（模拟左侧控制面板）
        left_spacer = QWidget()
        left_spacer.setFixedWidth(650)
        left_spacer.setStyleSheet("background-color: rgba(0, 20, 40, 0.3); border: 1px dashed #00FFFF;")
        left_label = QLabel("左侧控制面板区域")
        left_label.setAlignment(Qt.AlignCenter)
        left_layout = QVBoxLayout(left_spacer)
        left_layout.addWidget(left_label)
        interface_layout.addWidget(left_spacer)
        
        # 中间结果显示区域（模拟groupBox）
        self.result_widget = QWidget()
        self.result_widget.setFixedSize(320, 350)
        self.result_widget.setStyleSheet("""
            QWidget {
                border: 2px solid #00FFFF;
                border-radius: 5px;
                background-color: rgba(0, 34, 68, 0.7);
            }
        """)
        
        result_layout = QVBoxLayout(self.result_widget)
        result_title = QLabel("DD FS顺序优化结果")
        result_title.setAlignment(Qt.AlignCenter)
        result_title.setStyleSheet("font-size: 14px; color: #00FFFF; font-weight: bold;")
        result_layout.addWidget(result_title)
        
        self.result_text = QTextEdit()
        self.result_text.setStyleSheet("""
            QTextEdit {
                background-color: rgba(0, 20, 40, 0.8);
                color: white;
                font-size: 11px;
                border: 1px solid #00FFFF;
                border-radius: 2px;
            }
        """)
        self.result_text.setText("等待DQN算法计算结果...")
        result_layout.addWidget(self.result_text)
        
        interface_layout.addWidget(self.result_widget)
        
        # 右侧邻桶效应图表
        self.neighbor_chart = NeighborEffectChart()
        # 计算正确的图表宽度：总宽度 - 左侧 - 中间 - 边距
        chart_width = 1325 - 650 - 320 - 30
        self.neighbor_chart.setFixedSize(chart_width, 350)
        interface_layout.addWidget(self.neighbor_chart)
        
        main_layout.addWidget(interface_widget)
        
        # 控制按钮区域
        control_layout = QVBoxLayout()
        
        # 第一行：测试不同导弹数量
        row1_layout = QHBoxLayout()
        row1_label = QLabel("测试不同导弹数量的动态展示:")
        row1_label.setStyleSheet("font-size: 12px; color: #00FFFF; font-weight: bold;")
        row1_layout.addWidget(row1_label)
        
        test_cases_1 = [
            ("4发导弹", [1, 3, 2, 4], 4),
            ("8发导弹", [1, 3, 5, 7, 2, 4, 6, 8], 8),
            ("16发导弹", [1, 3, 5, 7, 9, 11, 13, 15, 2, 4, 6, 8, 10, 12, 14, 16], 16)
        ]
        
        for name, sequence, count in test_cases_1:
            btn = QPushButton(name)
            btn.clicked.connect(lambda checked, s=sequence, c=count, n=name: self.simulate_dqn_result(s, c, n))
            row1_layout.addWidget(btn)
        
        control_layout.addLayout(row1_layout)
        
        # 第二行：控制按钮
        row2_layout = QHBoxLayout()
        row2_label = QLabel("动态展示控制:")
        row2_label.setStyleSheet("font-size: 12px; color: #00FFFF; font-weight: bold;")
        row2_layout.addWidget(row2_label)
        
        self.pause_btn = QPushButton("暂停动画")
        self.pause_btn.clicked.connect(self.pause_animation)
        self.pause_btn.setEnabled(False)
        row2_layout.addWidget(self.pause_btn)
        
        self.resume_btn = QPushButton("继续动画")
        self.resume_btn.clicked.connect(self.resume_animation)
        self.resume_btn.setEnabled(False)
        row2_layout.addWidget(self.resume_btn)
        
        self.stop_btn = QPushButton("停止动画")
        self.stop_btn.clicked.connect(self.stop_animation)
        self.stop_btn.setEnabled(False)
        row2_layout.addWidget(self.stop_btn)
        
        clear_btn = QPushButton("清空图表")
        clear_btn.clicked.connect(self.clear_chart)
        row2_layout.addWidget(clear_btn)
        
        control_layout.addLayout(row2_layout)
        
        main_layout.addLayout(control_layout)
        
        # 状态显示
        self.status_label = QLabel("准备演示修复后的邻桶效应图表")
        self.status_label.setAlignment(Qt.AlignCenter)
        self.status_label.setStyleSheet("font-size: 12px; color: #00FFFF; padding: 10px; background-color: rgba(0, 34, 68, 0.5); border-radius: 5px;")
        main_layout.addWidget(self.status_label)
        
        # 动态展示相关
        self.dynamic_timer = QTimer()
        self.dynamic_timer.timeout.connect(self.update_dynamic_step)
        self.current_sequence = []
        self.current_missile_count = 0
        self.dynamic_step = 0
        self.is_paused = False
        
    def simulate_dqn_result(self, sequence, missile_count, name):
        """模拟DQN算法计算结果"""
        # 更新结果显示
        result_text = f"模拟DQN计算完成！\n\n"
        result_text += f"测试案例: {name}\n"
        result_text += f"导弹总数: {missile_count}发\n"
        result_text += f"最优发射序列: {sequence}\n"
        result_text += f"开始动态展示邻桶效应变化..."
        self.result_text.setText(result_text)
        
        # 启动动态展示
        self.start_dynamic_display(sequence, missile_count, name)
    
    def start_dynamic_display(self, sequence, missile_count, name):
        """启动动态展示"""
        self.current_sequence = sequence
        self.current_missile_count = missile_count
        self.dynamic_step = 0
        self.is_paused = False
        
        # 先清空图表
        self.neighbor_chart.clear_chart()
        
        # 更新状态和按钮
        self.status_label.setText(f"开始动态展示: {name} - 每0.8秒发射一发导弹")
        self.pause_btn.setEnabled(True)
        self.stop_btn.setEnabled(True)
        self.resume_btn.setEnabled(False)
        
        # 启动定时器
        self.dynamic_timer.start(800)
    
    def update_dynamic_step(self):
        """更新动态步骤"""
        if self.is_paused:
            return
            
        if self.dynamic_step > len(self.current_sequence):
            self.stop_animation()
            return
        
        # 获取当前已发射的导弹
        current_fired = self.current_sequence[:self.dynamic_step]
        
        # 更新图表
        self.neighbor_chart.update_chart(current_fired, self.current_missile_count)
        
        # 更新状态
        if self.dynamic_step == 0:
            status_text = f"初始状态 - 剩余{self.current_missile_count}发导弹，邻桶效应最大"
        elif self.dynamic_step <= len(self.current_sequence):
            fired_missile = self.current_sequence[self.dynamic_step - 1]
            remaining = self.current_missile_count - self.dynamic_step
            status_text = f"发射导弹{fired_missile} - 剩余{remaining}发导弹，邻桶效应递减"
        
        self.status_label.setText(status_text)
        self.dynamic_step += 1
    
    def pause_animation(self):
        """暂停动画"""
        self.is_paused = True
        self.pause_btn.setEnabled(False)
        self.resume_btn.setEnabled(True)
        self.status_label.setText("动画已暂停")
    
    def resume_animation(self):
        """继续动画"""
        self.is_paused = False
        self.pause_btn.setEnabled(True)
        self.resume_btn.setEnabled(False)
        self.status_label.setText("动画继续播放")
    
    def stop_animation(self):
        """停止动画"""
        self.dynamic_timer.stop()
        self.is_paused = False
        self.pause_btn.setEnabled(False)
        self.resume_btn.setEnabled(False)
        self.stop_btn.setEnabled(False)
        self.status_label.setText("动画已停止")
    
    def clear_chart(self):
        """清空图表"""
        self.stop_animation()
        self.neighbor_chart.clear_chart()
        self.result_text.setText("图表已清空，等待新的计算结果...")
        self.status_label.setText("图表已清空")

def main():
    app = QApplication(sys.argv)
    app.setStyle('Fusion')
    
    window = FixedNeighborEffectDemo()
    window.show()
    
    sys.exit(app.exec_())

if __name__ == "__main__":
    main()
