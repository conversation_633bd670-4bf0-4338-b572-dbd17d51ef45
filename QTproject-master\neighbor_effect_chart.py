#!/usr/bin/env python3
# -*- coding: utf-8 -*-

"""
邻桶效应可视化图表组件
"""

import numpy as np
import matplotlib.pyplot as plt
from matplotlib.backends.backend_qt5agg import FigureCanvasQTAgg as FigureCanvas
from matplotlib.figure import Figure
from PyQt5.QtWidgets import QWidget, QVBoxLayout, QLabel
from PyQt5.QtCore import Qt
import matplotlib
matplotlib.use('Qt5Agg')

# 设置中文字体
plt.rcParams['font.sans-serif'] = ['SimHei', 'Microsoft YaHei', 'DejaVu Sans']
plt.rcParams['axes.unicode_minus'] = False

class NeighborEffectChart(QWidget):
    def __init__(self, parent=None):
        super().__init__(parent)
        self.setMinimumSize(400, 350)
        self.setMaximumSize(700, 500)
        
        # 设置深色主题样式
        self.setStyleSheet("""
            QWidget {
                background-color: rgba(0, 20, 40, 0.8);
                border: 1px solid #00FFFF;
                border-radius: 5px;
            }
        """)
        
        # 创建布局
        layout = QVBoxLayout(self)
        layout.setContentsMargins(5, 5, 5, 5)
        layout.setSpacing(2)
        
        # 添加标题，调整字体大小适应更大空间
        self.title_label = QLabel("邻桶效应变化图")
        self.title_label.setAlignment(Qt.AlignCenter)
        self.title_label.setStyleSheet("""
            QLabel {
                color: #00FFFF;
                font-size: 16px;
                font-weight: bold;
                background-color: transparent;
                border: none;
                margin: 2px;
            }
        """)
        layout.addWidget(self.title_label)
        
        # 创建matplotlib图表，调整大小以适应更大空间
        self.figure = Figure(figsize=(5.5, 4.0), dpi=80)
        self.canvas = FigureCanvas(self.figure)
        self.canvas.setStyleSheet("background-color: transparent; border: none;")
        layout.addWidget(self.canvas)
        
        # 设置matplotlib深色主题
        plt.style.use('dark_background')
        self.figure.patch.set_facecolor('none')  # 透明背景
        
        # 初始化图表
        self.ax = self.figure.add_subplot(111)
        self.setup_chart()
        
        # 存储数据
        self.sequence = []
        self.missile_count = 0
        
    def setup_chart(self):
        """设置图表基本样式"""
        self.ax.clear()
        self.ax.set_facecolor('none')  # 透明背景

        # 设置标签和标题，调整字体大小适应更大空间
        self.ax.set_xlabel('已发射导弹数量', color='white', fontsize=12)
        self.ax.set_ylabel('邻桶效应值', color='white', fontsize=12)

        # 设置坐标轴颜色，调整字体大小
        self.ax.tick_params(colors='white', labelsize=10)
        self.ax.spines['bottom'].set_color('#00FFFF')
        self.ax.spines['top'].set_color('#00FFFF')
        self.ax.spines['left'].set_color('#00FFFF')
        self.ax.spines['right'].set_color('#00FFFF')

        # 设置网格
        self.ax.grid(True, alpha=0.3, color='gray', linestyle='--', linewidth=0.5)

        # 调整布局，适应更大空间
        self.figure.tight_layout(pad=1.0)
        
    def calculate_neighbor_effect(self, fired_count, total_missiles, sequence):
        """
        计算邻桶效应值
        
        参数:
        - fired_count: 已发射导弹数量
        - total_missiles: 总导弹数量
        - sequence: 发射序列
        
        返回:
        - effect_value: 邻桶效应值 (0.0 ~ 1.0)
        """
        if fired_count >= total_missiles:
            return 0.0
        
        # 计算剩余导弹数量
        remaining_missiles = total_missiles - fired_count
        
        # 使用指数衰减函数计算效应值
        # 基础效应：基于剩余导弹数量的比例
        base_effect = remaining_missiles / total_missiles
        
        # 位置效应：考虑剩余导弹的分布密度
        position_factor = self.calculate_position_factor(fired_count, sequence, total_missiles)
        
        # 邻接效应：考虑剩余导弹之间的相邻关系
        adjacency_factor = self.calculate_adjacency_factor(fired_count, sequence, total_missiles)
        
        # 综合计算效应值，使用非线性函数确保平滑过渡
        effect_value = base_effect * position_factor * adjacency_factor
        
        # 应用指数衰减使曲线更平滑
        decay_factor = np.exp(-0.3 * fired_count / total_missiles)
        effect_value *= decay_factor
        
        return max(0.0, min(1.0, effect_value))
    
    def calculate_position_factor(self, fired_count, sequence, total_missiles):
        """计算位置因子"""
        if fired_count == 0:
            return 1.0
        
        # 2×m布局，计算列数
        columns = total_missiles // 2
        
        # 获取已发射导弹的位置
        fired_missiles = sequence[:fired_count] if fired_count <= len(sequence) else sequence
        
        # 计算位置分散度
        positions = []
        for missile_id in fired_missiles:
            if missile_id % 2 == 1:  # 奇数ID在上排
                row, col = 0, (missile_id - 1) // 2
            else:  # 偶数ID在下排
                row, col = 1, (missile_id - 2) // 2
            positions.append((row, col))
        
        # 计算位置分散度（越分散，剩余导弹的干扰越小）
        if len(positions) <= 1:
            return 1.0
        
        # 计算位置方差作为分散度指标
        rows = [pos[0] for pos in positions]
        cols = [pos[1] for pos in positions]
        
        row_var = np.var(rows) if len(set(rows)) > 1 else 0
        col_var = np.var(cols) if len(set(cols)) > 1 else 0
        
        # 分散度越高，位置因子越小（效应降低更快）
        dispersion = (row_var + col_var) / 2
        position_factor = 1.0 - min(0.3, dispersion * 0.5)
        
        return position_factor
    
    def calculate_adjacency_factor(self, fired_count, sequence, total_missiles):
        """计算邻接因子"""
        if fired_count == 0:
            return 1.0
        
        columns = total_missiles // 2
        fired_missiles = set(sequence[:fired_count] if fired_count <= len(sequence) else sequence)
        
        # 计算剩余导弹之间的邻接关系
        remaining_missiles = set(range(1, total_missiles + 1)) - fired_missiles
        adjacency_count = 0
        total_pairs = 0
        
        for missile1 in remaining_missiles:
            for missile2 in remaining_missiles:
                if missile1 < missile2:  # 避免重复计算
                    total_pairs += 1
                    if self.are_adjacent(missile1, missile2, columns):
                        adjacency_count += 1
        
        if total_pairs == 0:
            return 0.0
        
        # 邻接比例越高，邻接因子越大（效应值越高）
        adjacency_ratio = adjacency_count / total_pairs
        return 0.3 + 0.7 * adjacency_ratio  # 确保最小值为0.3
    
    def are_adjacent(self, missile1, missile2, columns):
        """判断两个导弹是否相邻"""
        # 计算导弹位置
        def get_position(missile_id):
            if missile_id % 2 == 1:  # 奇数ID在上排
                return 0, (missile_id - 1) // 2
            else:  # 偶数ID在下排
                return 1, (missile_id - 2) // 2
        
        row1, col1 = get_position(missile1)
        row2, col2 = get_position(missile2)
        
        # 相邻条件：行差≤1且列差≤1，但不能是同一个位置
        row_diff = abs(row1 - row2)
        col_diff = abs(col1 - col2)
        
        return (row_diff <= 1 and col_diff <= 1) and not (row_diff == 0 and col_diff == 0)
    
    def update_chart(self, sequence, missile_count):
        """更新图表数据"""
        self.sequence = sequence
        self.missile_count = missile_count
        
        if not sequence or missile_count <= 0:
            self.setup_chart()
            self.canvas.draw()
            return
        
        # 计算效应值数据
        x_data = list(range(0, len(sequence) + 1))
        y_data = []
        
        for fired_count in x_data:
            effect_value = self.calculate_neighbor_effect(fired_count, missile_count, sequence)
            y_data.append(effect_value)
        
        # 清除并重新绘制
        self.setup_chart()
        
        # 绘制主曲线
        self.ax.plot(x_data, y_data, 'o-', color='#00FFFF', linewidth=2, 
                    markersize=4, markerfacecolor='#00FFFF', markeredgecolor='white',
                    markeredgewidth=0.5, label='邻桶效应')
        
        # 添加填充区域
        self.ax.fill_between(x_data, y_data, alpha=0.3, color='#00FFFF')
        
        # 设置坐标轴范围
        self.ax.set_xlim(-0.5, len(sequence) + 0.5)
        self.ax.set_ylim(0, 1.1)
        
        # 设置刻度
        self.ax.set_xticks(range(0, len(sequence) + 1, max(1, len(sequence) // 8)))
        self.ax.set_yticks([0, 0.2, 0.4, 0.6, 0.8, 1.0])
        
        # 添加关键点标注，调整字体大小适应更大空间
        if len(sequence) > 0:
            # 标注起始点
            self.ax.annotate(f'初始: {y_data[0]:.2f}',
                           xy=(0, y_data[0]), xytext=(5, 5),
                           textcoords='offset points', fontsize=10, color='yellow',
                           bbox=dict(boxstyle='round,pad=0.3', facecolor='black', alpha=0.7))

            # 标注结束点
            end_idx = len(sequence)
            self.ax.annotate(f'结束: {y_data[end_idx]:.2f}',
                           xy=(end_idx, y_data[end_idx]), xytext=(-35, 5),
                           textcoords='offset points', fontsize=10, color='yellow',
                           bbox=dict(boxstyle='round,pad=0.3', facecolor='black', alpha=0.7))

        # 添加图例，调整字体大小
        self.ax.legend(loc='upper right', fontsize=10, framealpha=0.8)
        
        # 更新标题
        self.title_label.setText(f"邻桶效应变化图 (共{missile_count}发导弹)")
        
        # 调整布局并刷新
        self.figure.tight_layout(pad=1.0)
        self.canvas.draw()
    
    def clear_chart(self):
        """清空图表"""
        self.setup_chart()
        self.title_label.setText("邻桶效应变化图")
        self.canvas.draw()
