#!/usr/bin/env python3
# -*- coding: utf-8 -*-

"""
演示资源状态对话框与DQN算法的集成
"""

import sys
import os

# 添加QTproject-master到路径
sys.path.append(os.path.join(os.path.dirname(__file__), 'QTproject-master'))

from PyQt5.QtWidgets import (QApplication, QPushButton, QVBoxLayout, 
                             QWidget, QLabel, QTextEdit, QHBoxLayout,
                             QSpinBox, QMessageBox)
from PyQt5.QtCore import Qt
from resource_dialog import ResourceDialog
from missile_launch_rl import get_optimal_sequence

class ResourceIntegrationDemo(QWidget):
    def __init__(self):
        super().__init__()
        self.setWindowTitle('资源状态与DQN算法集成演示')
        self.setGeometry(100, 100, 800, 600)
        
        # 存储资源状态
        self.resource_states = {}
        self.missile_count = 4  # 默认4发导弹
        
        self.setupUI()
    
    def setupUI(self):
        layout = QVBoxLayout()
        
        # 标题
        title = QLabel('资源状态与DQN算法集成演示')
        title.setAlignment(Qt.AlignCenter)
        title.setStyleSheet("font-size: 18px; font-weight: bold; margin: 10px;")
        layout.addWidget(title)
        
        # 导弹数量设置
        missile_layout = QHBoxLayout()
        missile_layout.addWidget(QLabel('导弹数量:'))
        self.missile_spinbox = QSpinBox()
        self.missile_spinbox.setMinimum(4)
        self.missile_spinbox.setMaximum(32)
        self.missile_spinbox.setValue(4)
        self.missile_spinbox.setSingleStep(4)  # 每次增加4
        self.missile_spinbox.valueChanged.connect(self.on_missile_count_changed)
        missile_layout.addWidget(self.missile_spinbox)
        missile_layout.addStretch()
        layout.addLayout(missile_layout)
        
        # 按钮区域
        button_layout = QHBoxLayout()
        
        # 设置资源状态按钮
        self.resource_btn = QPushButton('设置资源状态')
        self.resource_btn.clicked.connect(self.open_resource_dialog)
        self.resource_btn.setStyleSheet("QPushButton { background-color: #4CAF50; color: white; padding: 10px; }")
        button_layout.addWidget(self.resource_btn)
        
        # 计算最优序列按钮
        self.calculate_btn = QPushButton('计算最优发射序列')
        self.calculate_btn.clicked.connect(self.calculate_optimal_sequence)
        self.calculate_btn.setStyleSheet("QPushButton { background-color: #2196F3; color: white; padding: 10px; }")
        button_layout.addWidget(self.calculate_btn)
        
        layout.addLayout(button_layout)
        
        # 结果显示区域
        self.result_text = QTextEdit()
        self.result_text.setReadOnly(True)
        self.result_text.setPlainText("请先设置资源状态，然后计算最优发射序列。")
        layout.addWidget(self.result_text)
        
        self.setLayout(layout)
    
    def on_missile_count_changed(self, value):
        """导弹数量改变时的处理"""
        # 确保是4的倍数
        if value % 4 != 0:
            value = (value // 4) * 4
            self.missile_spinbox.setValue(value)
        
        self.missile_count = value
        # 清空之前的资源状态
        self.resource_states = {}
        self.result_text.setPlainText(f"导弹数量已设置为{value}发，请重新设置资源状态。")
    
    def open_resource_dialog(self):
        """打开资源设置对话框"""
        dialog = ResourceDialog(self, self.missile_count)
        if dialog.exec_():
            self.resource_states = dialog.getResources()
            
            # 显示设置的资源状态
            result_text = f"已设置{self.missile_count}发导弹的资源状态:\n\n"
            
            for missile_id in range(1, self.missile_count + 1):
                if missile_id in self.resource_states:
                    resource_data = self.resource_states[missile_id]
                    gas_status = resource_data.get('gas', 'OK')
                    water_status = resource_data.get('water', 'OK')
                    hydraulic_status = resource_data.get('hydraulic', 'OK')
                    
                    # 检查是否有故障
                    status_indicator = "✓" if all([gas_status == 'OK', water_status == 'OK', hydraulic_status == 'OK']) else "✗"
                    
                    result_text += f"DD{missile_id} {status_indicator}: 气源={gas_status}, 水源={water_status}, 液压源={hydraulic_status}\n"
                else:
                    result_text += f"DD{missile_id} ✓: 气源=OK, 水源=OK, 液压源=OK (默认)\n"
            
            result_text += "\n现在可以计算最优发射序列了。"
            self.result_text.setPlainText(result_text)
    
    def calculate_optimal_sequence(self):
        """计算最优发射序列"""
        try:
            # 检查是否有Tdistance.xlsx文件
            tdistance_path = "Tdistance.xlsx"
            if not os.path.exists(tdistance_path):
                QMessageBox.warning(self, "警告", "未找到Tdistance.xlsx文件，将使用模拟数据进行演示。")
            
            # 计算列数
            columns_count = self.missile_count // 2
            first_missile_id = 1  # 默认首发导弹为1号
            
            # 调用DQN算法
            sequence, score = get_optimal_sequence(
                tdistance_path, 
                columns_count, 
                first_missile_id, 
                verbose=False, 
                excluded_missiles=None,
                resource_states=self.resource_states
            )
            
            # 显示结果
            result_text = f"DQN算法计算结果:\n\n"
            result_text += f"导弹总数: {self.missile_count}发\n"
            result_text += f"首发导弹: DD{first_missile_id}\n"
            result_text += f"最优发射序列: {sequence}\n"
            result_text += f"序列评分: {score:.4f}\n\n"
            
            # 分析可用导弹
            available_missiles = []
            unavailable_missiles = []
            
            for missile_id in range(1, self.missile_count + 1):
                if missile_id in self.resource_states:
                    resource_data = self.resource_states[missile_id]
                    gas_ok = resource_data.get('gas', 'OK') == 'OK'
                    water_ok = resource_data.get('water', 'OK') == 'OK'
                    hydraulic_ok = resource_data.get('hydraulic', 'OK') == 'OK'
                    
                    if gas_ok and water_ok and hydraulic_ok:
                        available_missiles.append(missile_id)
                    else:
                        unavailable_missiles.append(missile_id)
                else:
                    available_missiles.append(missile_id)  # 默认可用
            
            result_text += f"可用导弹: {available_missiles}\n"
            if unavailable_missiles:
                result_text += f"不可用导弹: {unavailable_missiles} (资源故障)\n"
            
            result_text += f"\n实际参与发射的导弹数量: {len(sequence)}发"
            
            self.result_text.setPlainText(result_text)
            
        except Exception as e:
            QMessageBox.critical(self, "错误", f"计算过程中发生错误:\n{str(e)}")

if __name__ == '__main__':
    app = QApplication(sys.argv)
    window = ResourceIntegrationDemo()
    window.show()
    sys.exit(app.exec_())
