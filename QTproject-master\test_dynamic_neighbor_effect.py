#!/usr/bin/env python3
# -*- coding: utf-8 -*-

"""
测试动态邻桶效应图表功能
"""

import sys
from PyQt5.QtWidgets import QApplication, QMainWindow, QVBoxLayout, QWidget, QPushButton, QHBoxLayout, QLabel
from PyQt5.QtCore import Qt, QTimer
from neighbor_effect_chart import NeighborEffectChart

class DynamicTestWindow(QMainWindow):
    def __init__(self):
        super().__init__()
        self.setWindowTitle("动态邻桶效应图表测试")
        self.setGeometry(100, 100, 1000, 600)
        
        # 设置深色主题
        self.setStyleSheet("""
            QMainWindow {
                background-color: #1e1e1e;
                color: white;
            }
            QPushButton {
                background-color: #004293;
                color: white;
                border: 1px solid #00FFFF;
                border-radius: 5px;
                padding: 8px;
                font-size: 11px;
                margin: 2px;
            }
            QPushButton:hover {
                background-color: #0066cc;
            }
            QLabel {
                color: #00FFFF;
                font-size: 12px;
                padding: 5px;
            }
        """)
        
        # 创建中央部件
        central_widget = QWidget()
        self.setCentralWidget(central_widget)
        
        # 创建布局
        layout = QVBoxLayout(central_widget)
        
        # 添加说明
        info_label = QLabel("测试动态邻桶效应展示 - 模拟导弹按发射顺序逐步发射的过程")
        info_label.setAlignment(Qt.AlignCenter)
        layout.addWidget(info_label)
        
        # 创建水平布局来模拟主界面
        main_layout = QHBoxLayout()
        
        # 左侧模拟结果区域
        left_widget = QWidget()
        left_widget.setStyleSheet("""
            QWidget {
                border: 2px solid #00FFFF;
                border-radius: 5px;
                background-color: rgba(0, 34, 68, 0.7);
            }
        """)
        left_widget.setFixedSize(320, 350)
        
        left_layout = QVBoxLayout(left_widget)
        result_label = QLabel("模拟结果显示区域")
        result_label.setAlignment(Qt.AlignCenter)
        left_layout.addWidget(result_label)
        
        main_layout.addWidget(left_widget)
        
        # 右侧邻桶效应图表
        self.neighbor_chart = NeighborEffectChart()
        # 设置图表大小，模拟实际界面中的尺寸
        chart_width = 1000 - 320 - 30  # 总宽度 - 左侧宽度 - 间距
        self.neighbor_chart.setFixedSize(chart_width, 350)
        main_layout.addWidget(self.neighbor_chart)
        
        layout.addLayout(main_layout)
        
        # 控制按钮
        control_layout = QHBoxLayout()
        
        # 测试用例
        test_cases = [
            ("4发导弹动态", [1, 3, 2, 4], 4),
            ("8发导弹动态", [1, 3, 5, 7, 2, 4, 6, 8], 8),
            ("16发导弹动态", [1, 3, 5, 7, 9, 11, 13, 15, 2, 4, 6, 8, 10, 12, 14, 16], 16),
            ("停止动画", [], 0)
        ]
        
        for name, sequence, count in test_cases:
            btn = QPushButton(name)
            btn.clicked.connect(lambda checked, s=sequence, c=count, n=name: self.start_dynamic_test(s, c, n))
            control_layout.addWidget(btn)
        
        layout.addLayout(control_layout)
        
        # 状态显示
        self.status_label = QLabel("准备测试动态邻桶效应")
        self.status_label.setAlignment(Qt.AlignCenter)
        layout.addWidget(self.status_label)
        
        # 动态展示相关
        self.dynamic_timer = QTimer()
        self.dynamic_timer.timeout.connect(self.update_dynamic_step)
        self.current_sequence = []
        self.current_missile_count = 0
        self.dynamic_step = 0
        
    def start_dynamic_test(self, sequence, missile_count, name):
        """开始动态测试"""
        if not sequence:  # 停止动画
            self.stop_dynamic_test()
            return
            
        self.current_sequence = sequence
        self.current_missile_count = missile_count
        self.dynamic_step = 0
        
        # 先清空图表
        self.neighbor_chart.clear_chart()
        
        # 更新状态
        self.status_label.setText(f"开始动态展示: {name}")
        
        # 启动定时器，每800毫秒更新一次
        self.dynamic_timer.start(800)
    
    def update_dynamic_step(self):
        """更新动态步骤"""
        if self.dynamic_step > len(self.current_sequence):
            self.stop_dynamic_test()
            return
        
        # 获取当前已发射的导弹
        current_fired = self.current_sequence[:self.dynamic_step]
        
        # 更新图表
        self.neighbor_chart.update_chart(current_fired, self.current_missile_count)
        
        # 更新状态
        if self.dynamic_step == 0:
            self.status_label.setText(f"初始状态 - 剩余{self.current_missile_count}发导弹")
        elif self.dynamic_step <= len(self.current_sequence):
            fired_missile = self.current_sequence[self.dynamic_step - 1]
            remaining = self.current_missile_count - self.dynamic_step
            self.status_label.setText(f"发射导弹{fired_missile} - 剩余{remaining}发导弹")
        
        self.dynamic_step += 1
    
    def stop_dynamic_test(self):
        """停止动态测试"""
        self.dynamic_timer.stop()
        self.status_label.setText("动态展示完成")

def main():
    app = QApplication(sys.argv)
    app.setStyle('Fusion')
    
    window = DynamicTestWindow()
    window.show()
    
    sys.exit(app.exec_())

if __name__ == "__main__":
    main()
