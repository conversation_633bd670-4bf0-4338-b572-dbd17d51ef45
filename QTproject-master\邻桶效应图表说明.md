# 邻桶效应可视化图表实现说明

## 功能概述

本次实现为导弹发射序列优化系统添加了邻桶效应可视化图表功能，能够实时显示导弹发射过程中剩余导弹之间干扰效应的变化趋势。

## 核心特性

### 1. 数学模型
- **非线性函数**: 使用指数衰减、幂函数等数学模型计算邻桶效应
- **平滑曲线**: 确保效应值变化平滑，无突变点
- **多因子计算**: 综合考虑位置因子、邻接因子和基础效应

### 2. 可视化效果
- **实时更新**: 根据DQN算法结果实时更新图表
- **深色主题**: 与主界面保持一致的深色风格
- **专业外观**: 使用青色(#00FFFF)主题色，透明背景设计

### 3. 界面集成
- **独立空间**: 位于主界面右侧，不与结果区域重叠
- **自适应布局**: 自动调整大小以适应不同屏幕
- **无缝集成**: 与现有界面风格完全一致

## 文件结构

```
QTproject-master/
├── neighbor_effect_chart.py      # 邻桶效应图表组件
├── main.py                       # 主界面(已修改)
├── test_neighbor_effect.py       # 功能测试脚本
├── demo_neighbor_effect.py       # 演示程序
└── 邻桶效应图表说明.md           # 本说明文档
```

## 核心算法

### 邻桶效应计算公式

```python
effect_value = base_effect × position_factor × adjacency_factor × decay_factor
```

其中：
- **base_effect**: 基础效应 = 剩余导弹数量 / 总导弹数量
- **position_factor**: 位置因子，考虑已发射导弹的分布密度
- **adjacency_factor**: 邻接因子，考虑剩余导弹之间的相邻关系
- **decay_factor**: 衰减因子 = exp(-0.3 × 已发射数量 / 总数量)

### 位置因子计算
```python
# 计算已发射导弹位置的方差
row_var = np.var([导弹行坐标])
col_var = np.var([导弹列坐标])
dispersion = (row_var + col_var) / 2
position_factor = 1.0 - min(0.3, dispersion × 0.5)
```

### 邻接因子计算
```python
# 计算剩余导弹之间的邻接比例
adjacency_ratio = 相邻导弹对数 / 总导弹对数
adjacency_factor = 0.3 + 0.7 × adjacency_ratio
```

## 使用方法

### 1. 主程序集成
邻桶效应图表已集成到主界面中，当DQN算法计算完成后会自动更新显示。

### 2. 独立测试
```bash
# 运行功能测试
python test_neighbor_effect.py

# 运行演示程序
python demo_neighbor_effect.py
```

### 3. 手动更新
```python
# 在代码中手动更新图表
sequence = [1, 3, 5, 7, 2, 4, 6, 8]  # 发射序列
missile_count = 8                     # 导弹总数
self.update_neighbor_effect_chart(sequence, missile_count)
```

## 界面布局调整

### 原始布局
- groupBox位置: (650, 20, 550, 350)
- 占用整个右侧区域

### 调整后布局
- groupBox位置: (650, 20, 320, 350) - 结果显示区域
- neighbor_chart位置: (980, 20, 350, 350) - 邻桶效应图表

## 技术特点

### 1. 数学建模
- 使用多种数学函数确保曲线平滑
- 考虑2×m导弹布局的几何特性
- 实现非线性衰减效果

### 2. 性能优化
- 高效的邻接关系计算算法
- 最小化重复计算
- 实时响应用户操作

### 3. 用户体验
- 直观的可视化效果
- 清晰的数值标注
- 专业的图表样式

## 测试验证

### 测试用例
1. **4发导弹**: [1, 3, 2, 4]
2. **8发导弹**: [1, 3, 5, 7, 2, 4, 6, 8]
3. **16发导弹**: [1, 3, 5, 7, 9, 11, 13, 15, 2, 4, 6, 8, 10, 12, 14, 16]
4. **不同策略**: 顺序发射、交替发射、随机发射

### 验证结果
- ✅ 数学模型正确
- ✅ 曲线平滑无突变
- ✅ 界面集成完美
- ✅ 实时更新正常
- ✅ 深色主题一致

## 扩展功能

### 已实现
- 实时图表更新
- 多种测试模式
- 动态演示功能
- 数值标注显示

### 可扩展
- 添加更多数学模型
- 支持3D可视化
- 历史数据对比
- 导出图表功能

## 注意事项

1. **依赖库**: 需要matplotlib、numpy、PyQt5
2. **字体设置**: 已配置中文字体支持
3. **性能**: 大量导弹时计算复杂度较高
4. **兼容性**: 与现有代码完全兼容

## 总结

邻桶效应可视化图表成功实现了以下目标：
- 提供直观的效应变化展示
- 使用专业的数学建模
- 完美集成到现有界面
- 保持一致的视觉风格
- 支持实时动态更新

该功能为导弹发射序列优化系统增加了重要的可视化分析能力，有助于用户更好地理解和分析优化结果。
