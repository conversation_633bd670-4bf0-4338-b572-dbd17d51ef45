#!/usr/bin/env python3
# -*- coding: utf-8 -*-

"""
测试邻桶效应图表功能
"""

import sys
import os
from PyQt5.QtWidgets import QApplication, QMainWindow, QVBoxLayout, QWidget, QPushButton, QHBoxLayout
from PyQt5.QtCore import Qt
from neighbor_effect_chart import NeighborEffectChart

class TestWindow(QMainWindow):
    def __init__(self):
        super().__init__()
        self.setWindowTitle("邻桶效应图表测试")
        self.setGeometry(100, 100, 800, 600)
        
        # 设置深色主题
        self.setStyleSheet("""
            QMainWindow {
                background-color: #1e1e1e;
                color: white;
            }
            QPushButton {
                background-color: #004293;
                color: white;
                border: 1px solid #00FFFF;
                border-radius: 5px;
                padding: 10px;
                font-size: 12px;
            }
            QPushButton:hover {
                background-color: #0066cc;
            }
        """)
        
        # 创建中央部件
        central_widget = QWidget()
        self.setCentralWidget(central_widget)
        
        # 创建布局
        layout = QVBoxLayout(central_widget)
        
        # 创建邻桶效应图表
        self.neighbor_chart = NeighborEffectChart()
        layout.addWidget(self.neighbor_chart)
        
        # 创建测试按钮
        button_layout = QHBoxLayout()
        
        # 测试不同的发射序列
        test_cases = [
            ("4发导弹", [1, 3, 2, 4], 4),
            ("8发导弹", [1, 3, 5, 7, 2, 4, 6, 8], 8),
            ("16发导弹", [1, 3, 5, 7, 9, 11, 13, 15, 2, 4, 6, 8, 10, 12, 14, 16], 16),
            ("清空图表", [], 0)
        ]
        
        for name, sequence, count in test_cases:
            btn = QPushButton(name)
            btn.clicked.connect(lambda checked, s=sequence, c=count: self.test_sequence(s, c))
            button_layout.addWidget(btn)
        
        layout.addLayout(button_layout)
        
        # 初始显示
        self.test_sequence([1, 3, 2, 4], 4)
    
    def test_sequence(self, sequence, missile_count):
        """测试指定的发射序列"""
        print(f"测试序列: {sequence}, 导弹数量: {missile_count}")
        self.neighbor_chart.update_chart(sequence, missile_count)

def main():
    app = QApplication(sys.argv)
    
    # 设置应用程序样式
    app.setStyle('Fusion')
    
    window = TestWindow()
    window.show()
    
    sys.exit(app.exec_())

if __name__ == "__main__":
    main()
