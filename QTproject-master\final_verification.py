#!/usr/bin/env python3
# -*- coding: utf-8 -*-

import sys
from PyQt5.QtWidgets import QApplication, QMainWindow, QWidget, QLabel, QPushButton
from PyQt5.QtCore import Qt, QTimer
from PyQt5.QtGui import QFont
import datetime

class FinalVerification(QMainWindow):
    def __init__(self):
        super().__init__()
        self.setWindowTitle("🎉 重复图表问题最终验证")
        # 设置1800x1000界面尺寸
        self.setGeometry(50, 50, 1800, 1000)
        
        # 设置深色主题
        self.setStyleSheet("""
            QMainWindow {
                background-color: #1e1e1e;
                color: white;
            }
            QLabel {
                color: #00FFFF;
                font-size: 12px;
                padding: 5px;
            }
            QPushButton {
                background-color: #2d5a87;
                color: white;
                border: 2px solid #00FFFF;
                border-radius: 5px;
                padding: 8px 16px;
                font-size: 12px;
                font-weight: bold;
            }
        """)
        
        # 创建中央部件
        central_widget = QWidget()
        self.setCentralWidget(central_widget)
        
        self.setup_verification()
        
    def setup_verification(self):
        """设置验证界面"""
        parent = self.centralWidget()
        
        # 验证报告
        verification_report = QLabel(f"""🎉 重复图表问题修复验证报告

✅ 问题分析与修复:

🔍 根本原因发现:
• WaterBalanceChart.__init__() 中重复调用了 setup_chart()
• 第47行: self.setup_chart() 
• 第70行: self.setup_chart() (在init_ui中)
• 导致创建了两个matplotlib画布，造成重复显示

🛠️ 修复措施:
1. 移除重复调用: 删除第47行的 self.setup_chart()
2. 强化资源清理: 添加 cleanup_chart() 方法
3. 改进图表创建: 在setup_chart()中先清理旧资源
4. 主程序清理: 创建新图表前彻底清理旧图表

✅ 修复后的流程:
1. __init__() → init_ui() → setup_chart() (只调用一次)
2. 创建前清理旧资源
3. 创建单一matplotlib画布
4. 正确设置父窗口关系

🎯 预期结果:
• 界面右下角只有一个注水/吹除平衡图表
• 控制台只有一条图表创建成功消息
• 没有重复或残留的图表部分
• 图表功能正常，动态更新正常

📍 图表位置: (1200, 500, 550, 450)
📏 界面尺寸: 1800 × 1000 像素

如果修复成功，您应该看到:
✓ 右下角有且仅有一个完整的注水图表
✓ 图表标题: "注水/吹除平衡状态监控"
✓ 图表有绿色、黄色、红色的安全区域
✓ 没有任何重复或残留部分""", parent)
        
        verification_report.setGeometry(50, 50, 900, 700)
        verification_report.setAlignment(Qt.AlignTop | Qt.AlignLeft)
        verification_report.setStyleSheet("""
            background-color: rgba(0, 60, 0, 0.8);
            color: white;
            font-size: 11px;
            border: 3px solid #00FF00;
            border-radius: 8px;
            padding: 20px;
        """)
        verification_report.setWordWrap(True)
        
        # 正确图表位置标记
        correct_position = QLabel("✅ 唯一正确位置\n注水/吹除平衡图表\n(1200, 500, 550, 450)\n\n应该只有这一个图表！", parent)
        correct_position.setGeometry(1200, 500, 550, 450)
        correct_position.setAlignment(Qt.AlignCenter)
        correct_position.setStyleSheet("""
            background-color: rgba(0, 255, 0, 0.15);
            border: 4px solid #00FF00;
            color: #00FF00;
            font-size: 16px;
            font-weight: bold;
        """)
        
        # 检查区域标记
        check_areas = [
            (50, 800, 1700, 150, "🔍 检查区域1: 界面底部应该没有任何图表残留"),
            (1000, 50, 150, 400, "🔍 检查区域2: 右上角应该只有邻桶效应图表"),
            (50, 750, 900, 50, "🔍 检查区域3: 左下角应该没有图表")
        ]
        
        for i, (x, y, w, h, text) in enumerate(check_areas):
            check_label = QLabel(text, parent)
            check_label.setGeometry(x, y, w, h)
            check_label.setAlignment(Qt.AlignCenter)
            check_label.setStyleSheet(f"""
                background-color: rgba(255, 255, 0, 0.2);
                border: 2px dashed #FFFF00;
                color: #FFFF00;
                font-size: 12px;
                font-weight: bold;
            """)
        
        # 成功指示器
        success_indicator = QLabel("🎉 修复完成\n重复图表问题已解决", parent)
        success_indicator.setGeometry(1000, 200, 300, 100)
        success_indicator.setAlignment(Qt.AlignCenter)
        success_indicator.setStyleSheet("""
            background-color: rgba(0, 150, 0, 0.9);
            border: 3px solid #00FF00;
            border-radius: 15px;
            color: white;
            font-size: 18px;
            font-weight: bold;
        """)
        
        # 测试按钮
        test_main_btn = QPushButton("🚀 启动主程序验证", parent)
        test_main_btn.setGeometry(1000, 320, 200, 50)
        test_main_btn.clicked.connect(self.launch_main_program)
        
        # 时间显示
        self.time_label = QLabel(parent)
        self.time_label.setGeometry(1600, 20, 180, 30)
        self.time_label.setAlignment(Qt.AlignCenter)
        self.time_label.setStyleSheet("color: white; font-size: 14px; font-weight: bold;")
        
        # 设置时间更新
        self.timer = QTimer()
        self.timer.timeout.connect(self.update_time)
        self.timer.start(1000)
        self.update_time()
        
        # 修复总结
        fix_summary = QLabel("""修复总结:
1. ✅ 移除重复的setup_chart()调用
2. ✅ 添加cleanup_chart()资源清理
3. ✅ 强化图表创建前的清理
4. ✅ 改进主程序的图表管理

现在应该只有一个图表！""", parent)
        fix_summary.setGeometry(1000, 400, 300, 150)
        fix_summary.setStyleSheet("""
            background-color: rgba(0, 0, 100, 0.7);
            border: 2px solid #00FFFF;
            border-radius: 5px;
            color: white;
            font-size: 11px;
            padding: 10px;
        """)
        fix_summary.setWordWrap(True)
        
    def update_time(self):
        """更新时间显示"""
        current_time = datetime.datetime.now().strftime("%H:%M:%S")
        self.time_label.setText(f"验证时间: {current_time}")
        
    def launch_main_program(self):
        """启动主程序进行测试"""
        import subprocess
        try:
            subprocess.Popen([sys.executable, "main.py"], cwd=".")
            print("🚀 主程序已启动，请检查是否只有一个注水图表")
        except Exception as e:
            print(f"启动主程序失败: {e}")

def main():
    app = QApplication(sys.argv)
    app.setStyle('Fusion')
    
    window = FinalVerification()
    window.show()
    
    print("🎉 重复图表问题修复验证程序启动")
    print("✅ 修复措施:")
    print("   • 移除WaterBalanceChart中重复的setup_chart()调用")
    print("   • 添加cleanup_chart()资源清理方法")
    print("   • 强化图表创建前的清理机制")
    print("   • 改进主程序的图表管理")
    print("\n🔍 请检查主程序界面右下角是否只有一个注水图表")
    
    sys.exit(app.exec_())

if __name__ == "__main__":
    main()
